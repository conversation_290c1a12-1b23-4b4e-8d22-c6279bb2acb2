<div>
  <div class="px-3 pt-3">
    <button
      class="btn btn-primary"
      type="button"
      (click)="downloadPDF()"
      [disabled]="isGeneratingPDF()"
    >
      {{ isGeneratingPDF() ? "Generating PDF..." : "Download PDF" }}
    </button>
  </div>
  <div id="handover-form">
    <form
      *ngIf="vehicleBookingForm"
      [formGroup]="vehicleBookingForm"
      (ngSubmit)="handleSubmit()"
      [class.disabled]="isSubmitting()"
      [attr.disabled]="isSubmitting()"
    >
      <div id="formContent" class="container mt-4">
        <!-- Payment Validation Section -->
        <div class="card mb-4" *ngIf="!paymentValidated()">
          <div class="card-header">
            <h5 class="mb-0">Payment Validation</h5>
          </div>
          <div class="card-body">
            <div
              class="alert"
              [ngClass]="{
                'alert-info': !isBookingFullyPaid(),
                'alert-success': isBookingFullyPaid(),
              }"
            >
              <i class="fas fa fa-check-circle mr-2"></i>
              <span *ngIf="isBookingFullyPaid()"
                >This booking is already fully paid. You can skip payment capture.</span
              >
              <span *ngIf="!isBookingFullyPaid()"
                >Please verify all payments and deposits before proceeding with the vehicle
                handover.</span
              >
            </div>

            <!-- Invoice Details -->
            <div class="mb-4">
              <h6 class="text-core-blue mb-3">Invoice Details</h6>
              <div class="table-responsive">
                <table class="table table-bordered">
                  <thead class="bg-light">
                    <tr>
                      @let baseCurrency = getHeaderCurrency();
                      <th>Invoice ID</th>
                      <th>Total Amount {{ baseCurrency }}</th>
                      <th>Amount Paid {{ baseCurrency }}</th>
                      <th>Balance Due {{ baseCurrency }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let invoice of log?.invoices">
                      <td>{{ invoice.id }}</td>
                      <td>{{ invoice.totalAmount | contextualCurrency: invoice }}</td>
                      <td>{{ getTotalPaid(invoice) | contextualCurrency: invoice }}</td>
                      <td>{{ getBalanceDue(invoice) | contextualCurrency: invoice }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Payment Confirmation -->
            <div class="mb-4">
              <h6 class="text-core-blue mb-3">Payment Confirmation</h6>
              <div [class.disabled]="isBookingFullyPaid()" class="form-group">
                <label for="amountPaid">Amount Paid Today</label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text" style="font-size: 0.8rem">{{
                      getHeaderCurrency(false)
                    }}</span>
                  </div>
                  <input
                    type="number"
                    id="amountPaid"
                    formControlName="amountPaid"
                    class="form-control"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    [disabled]="isBookingFullyPaid()"
                  />
                </div>
                <div
                  *ngIf="
                    vehicleBookingForm.get('amountPaid')?.invalid &&
                    vehicleBookingForm.get('amountPaid')?.touched
                  "
                  class="text-danger mt-1"
                >
                  <small *ngIf="vehicleBookingForm.get('amountPaid')?.errors?.['required']"
                    >Amount paid is required</small
                  >
                  <small *ngIf="vehicleBookingForm.get('amountPaid')?.errors?.['min']"
                    >Amount must be greater than or equal to 0</small
                  >
                </div>
              </div>

              <div [class.disabled]="isBookingFullyPaid()" class="form-group mt-3">
                <label for="paymentMethod">Payment Method</label>
                <select
                  id="paymentMethod"
                  formControlName="paymentMethod"
                  class="form-control"
                  [disabled]="isBookingFullyPaid()"
                >
                  <option value="">Select payment method</option>
                  <option value="CASH">Cash</option>
                  <option value="CARD">Card</option>
                  <option value="BANK_TRANSFER">Bank Transfer</option>
                  <option value="MOBILE_MONEY">Mobile Money</option>
                </select>
                <div
                  *ngIf="
                    vehicleBookingForm.get('paymentMethod')?.invalid &&
                    vehicleBookingForm.get('paymentMethod')?.touched
                  "
                  class="text-danger mt-1"
                >
                  <small *ngIf="vehicleBookingForm.get('paymentMethod')?.errors?.['required']"
                    >Payment method is required</small
                  >
                </div>
              </div>

              <div [class.disabled]="isBookingFullyPaid()" class="form-group mt-3">
                <label for="paymentReference">Payment Reference</label>
                <input
                  type="text"
                  id="paymentReference"
                  formControlName="paymentReference"
                  class="form-control"
                  placeholder="Transaction ID, Receipt Number, etc."
                  [disabled]="isBookingFullyPaid()"
                />
              </div>
            </div>

            <!-- Deposit Confirmation -->
            <div class="mb-4">
              <h6 class="text-core-blue mb-3">Deposit Confirmation</h6>
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Required deposit amount:
                <strong>{{ log?.vehicle?.depositAmt | contextualCurrency: log }}</strong>
              </div>

              <div class="form-group">
                <label for="depositPaid">Deposit Amount Received</label>
                <div class="input-group">
                  <div class="input-group-prepend">
                    <span class="input-group-text" style="font-size: 0.8rem">{{
                      getHeaderCurrency(false)
                    }}</span>
                  </div>
                  <input
                    type="number"
                    id="depositPaid"
                    formControlName="depositPaid"
                    class="form-control"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    [disabled]="isBookingFullyPaid()"
                  />
                </div>
                <div
                  *ngIf="
                    vehicleBookingForm.get('depositPaid')?.invalid &&
                    vehicleBookingForm.get('depositPaid')?.touched
                  "
                  class="text-danger mt-1"
                >
                  <small *ngIf="vehicleBookingForm.get('depositPaid')?.errors?.['required']"
                    >Deposit amount is required</small
                  >
                  <small *ngIf="vehicleBookingForm.get('depositPaid')?.errors?.['min']"
                    >Amount must be greater than or equal to 0</small
                  >
                </div>
                <small class="text-muted mt-1">
                  This deposit is returnable when the vehicle is returned in good condition.
                </small>
              </div>

              <div class="form-group mt-3">
                <label for="depositMethod">Deposit Payment Method</label>
                <select
                  id="depositMethod"
                  formControlName="depositMethod"
                  class="form-control"
                  [disabled]="isBookingFullyPaid()"
                >
                  <option value="">Select payment method</option>
                  <option value="CASH">Cash</option>
                  <option value="CARD">Card</option>
                  <option value="BANK_TRANSFER">Bank Transfer</option>
                  <option value="MOBILE_MONEY">Mobile Money</option>
                </select>
                <div
                  *ngIf="
                    vehicleBookingForm.get('depositMethod')?.invalid &&
                    vehicleBookingForm.get('depositMethod')?.touched
                  "
                  class="text-danger mt-1"
                >
                  <small *ngIf="vehicleBookingForm.get('depositMethod')?.errors?.['required']"
                    >Deposit method is required</small
                  >
                </div>
              </div>

              <div class="form-group mt-3">
                <label for="depositReference">Deposit Reference</label>
                <input
                  type="text"
                  id="depositReference"
                  formControlName="depositReference"
                  class="form-control"
                  placeholder="Transaction ID, Receipt Number, etc."
                  [disabled]="isBookingFullyPaid()"
                />
              </div>
            </div>

            <!-- Validation Button -->
            <div class="text-center mt-4">
              <button
                type="button"
                class="btn btn-danger btn-lg px-5"
                (click)="skipPaymentsCapture()"
              >
                <i class="fas fa-times-circle mr-2"></i>
                Skip Payments Capture
              </button>

              <button
                type="button"
                class="btn btn-primary btn-lg px-5"
                (click)="validatePayments()"
                [disabled]="!isPaymentFormValid() || isValidatingPayment()"
              >
                <i class="fas fa-check-circle mr-2"></i>
                {{ isValidatingPayment() ? "Validating..." : "Validate Payments & Continue" }}
              </button>
            </div>
          </div>
        </div>

        <!-- Vehicle Condition Section (shown after payment validation) -->
        <div *ngIf="paymentValidated()">
          <div class="row align-items-center justify-content-between">
            <!-- Left Section: Title -->
            <div class="col-5">
              <h4 class="fw-bold">Vehicle Condition Report Form - OUT</h4>
            </div>

            <div class="col-2">
              <div class="d-flex mx-auto align-items-center">
                <img
                  src="../../../../../../assets/images/Vector.png"
                  alt="Logo"
                  class="img-fluid logo"
                />
              </div>
            </div>
            <div class="col">
              <div class="d-flex align-items-center mr-4">
                <img
                  src="../../../../../../assets/images/Group 797.png"
                  alt="No Smoking"
                  class="img-fluid restriction-icon"
                />
              </div>
              <p class="mb-0 ms-2">No smoking in car</p>
            </div>

            <div class="col">
              <div class="d-flex align-items-center">
                <img
                  src="../../../../../../assets/images/Group 795.png"
                  alt="No Animals"
                  class="img-fluid restriction-icon"
                />
              </div>
              <p class="mb-0 ms-2">No animals in car</p>
            </div>
          </div>

          <hr />

          <div class="container mt-4">
            <!-- Customer and Vehicle Information Card -->
            <div class="card bg-light p-3 mb-3">
              <!-- Details Row -->
              <div class="row">
                <!-- Booking Details Column -->
                <div class="col-md-4">
                  <h5 class="text-core-blue mb-3">Booking Details</h5>
                  <div class="mb-2">
                    <i class="fas fa-hashtag text-muted"></i>
                    <span class="ms-2">{{ log.id }}</span>
                  </div>
                  <div class="mb-2">
                    <i class="fas fa-calendar-alt text-muted"></i>
                    <span class="ms-2">{{ log.start | date: "d MMMM y hh:mm a" }}</span>
                  </div>
                  <div class="mb-2">
                    <i class="fas fa-calendar-check text-muted"></i>
                    <span class="ms-2">{{ log.end | date: "d MMMM y hh:mm a" }}</span>
                  </div>

                  <!-- Promotion Information -->
                  <div *ngIf="log.promotion != null" class="promotion-section">
                    <h6 class="promotion-title"><i class="fas fa-tag"></i>Promotion Applied</h6>
                    <div class="promotion-item">
                      <strong>{{ log.promotion.title || "Discount" }}</strong>
                      <div class="promotion-description">
                        {{ log.promotion.description || "Special offer applied to this booking" }}
                      </div>
                      <div>
                        <span
                          *ngIf="
                            log.promotion.promotionType === 'DISCOUNT' ||
                            !log.promotion.promotionType
                          "
                          class="promotion-badge bg-success text-white"
                          >{{ log.promotion.discount }}% Off</span
                        >
                        <span
                          *ngIf="log.promotion.promotionType === 'EXTRA_MILEAGE'"
                          class="promotion-badge bg-info text-white"
                          >+{{ log.promotion.extraMileage }} km</span
                        >
                        <span
                          *ngIf="log.promotion.promotionType === 'EXTRA_DAYS'"
                          class="promotion-badge bg-warning text-dark"
                          >+{{ log.promotion.extraDays }} free days</span
                        >
                      </div>
                    </div>
                  </div>

                  <div class="mt-3">
                    <button
                      type="button"
                      class="btn btn-primary btn-sm"
                      (click)="openModal(editModal)"
                    >
                      Edit Booking
                    </button>
                  </div>
                </div>

                <!-- Vehicle Details Column -->
                <div class="col-md-3">
                  <h5 class="text-core-blue mb-3">Vehicle Details</h5>
                  <div class="mb-2">
                    <i class="fas fa-car text-muted"></i>
                    <span class="ms-2">{{ log.vehicle?.name }} {{ log.vehicle?.model }}</span>
                  </div>
                  <div class="mb-2">
                    <i class="fas fa-hashtag text-muted"></i>
                    <span class="ms-2">{{ log.vehicle?.regno }}</span>
                  </div>
                  <div class="mb-2">
                    <i class="fas fa-tachometer-alt text-muted"></i>
                    <span class="ms-2">{{ log.vehicle?.mileage }}KM</span>
                  </div>
                </div>

                <!-- Hirer Details Column -->
                <div class="col-md-5">
                  <h5 class="text-core-blue mb-3">Hirer Details</h5>

                  <!-- Primary Contact Information -->
                  <div class="mb-3">
                    <h5 class="mb-2">
                      {{ log.client?.name || "Name not provided" }}
                    </h5>
                    <div class="mb-2">
                      <i class="fas fa-envelope text-muted"></i>
                      <span class="ms-2">{{ log.client?.email || "Email not provided" }}</span>
                    </div>
                    <div class="mb-2">
                      <i class="fas fa-phone text-muted"></i>
                      <span class="ms-2">{{
                        log.client?.telephone || "Phone number not provided"
                      }}</span>
                    </div>
                  </div>

                  <!-- Additional Details Grid -->
                  <div class="row mb-3">
                    <div class="col-6">
                      <div class="mb-2">
                        <i class="fas fa-star text-warning"></i>
                        <span class="ms-2">{{
                          log.client?.rating
                            ? log.client.rating.toFixed(1) + " stars"
                            : "Not rated yet"
                        }}</span>
                      </div>
                      <div class="mb-2">
                        <i
                          class="fas fa-check-circle"
                          [ngClass]="log.client?.verified ? 'text-primary' : 'text-muted'"
                        ></i>
                        <span class="ms-2">{{
                          log.client?.verified ? "Verified Hirer" : "Unverified Hirer"
                        }}</span>
                      </div>
                    </div>
                    <div class="col-6">
                      <div class="mb-2">
                        <i class="fas fa-id-card text-muted"></i>
                        <span class="ms-2">{{
                          getDocumentByType("DRIVER") ? "License uploaded" : "License not uploaded"
                        }}</span>
                      </div>
                      <div class="mb-2">
                        <i class="fas fa-map-marker-alt text-muted"></i>
                        <span class="ms-2">{{
                          formatAddress(log.client?.address) || "Address not provided"
                        }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Action Button -->
                  <div>
                    <button
                      type="button"
                      class="btn btn-primary btn-sm"
                      (click)="viewHirerDetails()"
                    >
                      View hirer details
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Check Out Items Card -->
            <div class="card bg-light p-3 mb-3">
              <!-- Side Heading -->
              <div class="row">
                <div class="col-md-3 text-end pe-3 border-end">
                  <h5 class="text-core-blue font-weight-bold">Check out items</h5>
                </div>
              </div>
              <div class="inventory-items-grid">
                <div
                  *ngFor="let inventory of vehicleInventories; let i = index"
                  class="inventory-item"
                >
                  <input
                    class="p-0 m-0"
                    type="checkbox"
                    [checked]="inventory.selected"
                    (change)="toggleInventorySelection(i)"
                  />
                  <label class="m-0">{{ inventory.name }}</label>
                </div>
              </div>
            </div>
          </div>

          <div *ngIf="log.status === 'COMPLETE'" class="card-header">
            <div class="bg-custom-danger text-white p-2">
              <strong>Handover Information</strong>
            </div>
          </div>

          <div class="row">
            <div class="col">
              <div class="row fuel-container d-flex align-items-center justify-content-between">
                <!-- Left Section: Fuel Level Label -->
                <div class="col-2 fuel-label text-muted">
                  <span>Fuel level Out</span>
                </div>

                <!-- Middle Section: Slider -->
                <div class="col-6 slider-container text-center">
                  <p class="slider-text">Move slider</p>
                  <div class="slider-wrapper">
                    <input
                      type="range"
                      min="0"
                      max="10"
                      step="1"
                      id="fractionSlider"
                      [value]="vehicleBookingForm.get('fuelOut')?.value"
                      class="fuel-slider"
                      (input)="updateSliderValueOut($event)"
                    />
                    <div class="slider-labels">
                      <span class="text-danger">0</span>
                      <span>1</span>
                      <span>2</span>
                      <span>3</span>
                      <span>4</span>
                      <span>5</span>
                      <span>6</span>
                      <span>7</span>
                      <span>8</span>
                      <span>9</span>
                      <span class="text-success">10</span>
                    </div>
                  </div>
                </div>

                <div class="col-2 fuel-type text-end">
                  <span>Fuel Type: </span>
                  <strong class="fuel-value">{{ log.vehicle.fuelType }}</strong>
                </div>
              </div>
            </div>
          </div>
          <br />
          <br />

          <div class="row">
            <div class="col">
              <div class="row fuel-container d-flex align-items-center justify-content-between">
                <div class="col-3">
                  <label for="mileageOut" class="form-label fw-light text-muted">Mileage out</label>
                  <input
                    name="mileageOut"
                    type="number"
                    formControlName="mileageOut"
                    class="form-control"
                    placeholder="Enter mileage"
                  />
                  <div
                    *ngIf="vehicleBookingForm.get('mileageOut')?.invalid && hasSubmitted()"
                    class="text-danger mt-1 fs-small"
                  >
                    <div *ngIf="vehicleBookingForm.get('mileageOut')?.hasError('required')">
                      Mileage out is required
                    </div>
                    <div *ngIf="vehicleBookingForm.get('mileageOut')?.hasError('min')">
                      Mileage out must be greater than the current vehicle mileage ({{
                        log.vehicle?.mileage
                      }}KM)
                    </div>
                  </div>
                </div>
                <div class="col d-flex align-items-center">
                  <span class="text-muted ms-2">
                    &#9432; The hirer is responsible for all fuel used once leaving our branch<br />
                    @let limit = getReturnMileageLimit();
                    @if (limit === 0) {
                      There is no return mileage limit
                    } @else {
                      The return mileage limit is
                      <strong>{{ getReturnMileageLimit() }}km</strong>
                    }
                  </span>
                </div>
              </div>
            </div>
            <!-- Mileage Input Section -->
            <br />

            <br />

            <div #imageContainer class="image-container">
              <img
                src="../../../../../../assets/images/vehicle damage mapping.jpg"
                alt="Vehicle Damage Map"
                (click)="handleImageClick($event)"
              />

              <!-- Comment Markers -->
              <div
                *ngFor="let comment of comments"
                class="comment-marker"
                [style.left.px]="comment.x"
                [style.top.px]="comment.y"
                (click)="selectComment(comment, $event)"
              >
                {{ comments.indexOf(comment) + 1 }}
              </div>

              <!-- New Comment Input -->
              <div
                *ngIf="newCommentPosition"
                class="comment-input"
                [style.left.px]="newCommentPosition.x + 20"
                [style.top.px]="newCommentPosition.y"
              >
                <textarea
                  formControlName="newCommentText"
                  class="form-control"
                  placeholder="Add your comment..."
                  rows="3"
                  [(ngModel)]="newCommentText"
                ></textarea>

                <button class="btn btn-primary btn-sm" (click)="addComment()">Add</button>
                <button class="btn btn-secondary btn-sm" (click)="cancelNewComment()">
                  Cancel
                </button>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col">
              <div class="row fuel-container d-flex align-items-center justify-content-between">
                <strong>
                  In keeping with the industry standards, short term vehicle rental does not have a
                  margin of fair wear and tear for damage. Therefore, any change to the condition of
                  the vehicle during the rental is the responsibility of the customer.
                </strong>
              </div>
            </div>
          </div>

          <div class="container mt-4">
            <!-- Vehicle Handover Agreement Section -->
            <div class="row" *ngIf="log.status === 'BOOKED'">
              <div class="col-6">
                <div class="card-header">
                  <div class="bg-custom-danger text-white p-2">
                    <strong>Vehicle Handover Agreement</strong>
                  </div>
                </div>
                <div class="card-body">
                  <p class="text-muted">
                    By signing this document, you agree to the terms and conditions of car rental
                    via KarLink. It is your responsibility to inspect the vehicle and to take
                    photographs to show any change of condition, or anything you are unsure of. You
                    should take as long as you wish to complete your inspection of both the exterior
                    (including wheels and tyres) and interior of the vehicle. You should ensure you
                    are satisfied with the condition of the vehicle before you drive away from the
                    location in line with the damage thresholds Any discrepancies must be emailed
                    immediately to:
                    <a href="mailto:<EMAIL>" class="text-primary"
                      >support&#64;karlink.uk.</a
                    >
                  </p>
                </div>
              </div>

              <div class="col-6">
                <div class="table-responsive mt-2">
                  <table class="table table-bordered">
                    <thead class="bg-custom-danger text-white p-2">
                      <tr class="card-header">
                        <th style="width: 10%">Area</th>
                        <!-- Reduce width -->
                        <th style="width: 65%">Description</th>
                        <!-- Increase width -->
                        <th style="width: 20%">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngIf="!damageInfoOut.controls.length">
                        <td colspan="3" class="text-center text-muted">
                          No damage information recorded.
                        </td>
                      </tr>
                      <tr
                        *ngFor="let comment of damageInfoOut.controls; let i = index"
                        [formGroup]="comment"
                      >
                        <td>
                          <strong>{{ i + 1 }}</strong>
                        </td>
                        <td>
                          <strong class="mb-1 d-block text-wrap">{{
                            comment.value.description
                          }}</strong>
                        </td>
                        <td class="text-center">
                          <button
                            type="button"
                            class="btn btn-danger btn-sm"
                            (click)="removeDamage(i)"
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Signature Section -->
            <div class="card p-3 mb-4">
              <div class="row align-items-stretch">
                <!-- Customer Signature -->
                <div class="col-5 d-flex flex-column">
                  <h6>Customer Signature</h6>
                  <div
                    class="border border-dark rounded p-3 flex-grow-1"
                    style="cursor: pointer; height: 150px"
                    (click)="openCustomerSignatureModal(customerSignatureModal)"
                  >
                    <img
                      *ngIf="savedCustomerSignature"
                      [src]="savedCustomerSignature"
                      alt="Customer Signature"
                      class="img-fluid h-100"
                      style="object-fit: contain"
                    />
                    <p *ngIf="!savedCustomerSignature" class="text-muted">Tap to sign</p>
                  </div>

                  <!-- Validation error for signatureOutHirer -->
                  <div
                    class="text-danger mt-1 fs-small"
                    *ngIf="vehicleBookingForm.get('signatureOutHirer')?.invalid && hasSubmitted()"
                  >
                    <div *ngIf="vehicleBookingForm.get('signatureOutHirer')?.hasError('required')">
                      Customer signature is required
                    </div>
                  </div>

                  <!-- Customer Name & Date Section -->
                  <div class="row w-100 mt-2 px-3">
                    <div class="d-flex flex-column">
                      <span>{{ log.firstname }} {{ log.surname }}</span>
                      <span>{{ today | date }}</span>
                    </div>
                  </div>
                </div>

                <!-- Spacer Column -->
                <div class="col-2 d-flex align-items-center justify-content-center">
                  <div class="text-muted">AND</div>
                </div>

                <!-- Representative Signature -->
                <div class="col-5 d-flex flex-column">
                  <h6>Representative Signature</h6>
                  <div
                    class="border border-dark rounded p-3 flex-grow-1"
                    style="cursor: pointer; height: 150px"
                    (click)="openSignatureModal(signatureModal)"
                  >
                    <img
                      *ngIf="savedSignature"
                      [src]="savedSignature"
                      alt="Representative Signature"
                      class="img-fluid h-100"
                      style="object-fit: contain"
                    />
                    <p *ngIf="!savedSignature" class="text-muted">Tap to sign</p>
                  </div>

                  <!-- Validation error for signatureOutCarRental -->
                  <div
                    class="text-danger mt-1 fs-small"
                    *ngIf="
                      vehicleBookingForm.get('signatureOutCarRental')?.invalid && hasSubmitted()
                    "
                  >
                    <div
                      *ngIf="vehicleBookingForm.get('signatureOutCarRental')?.hasError('required')"
                    >
                      Representative signature is required
                    </div>
                  </div>

                  <!-- Representative Name & Date Section -->
                  <div class="row w-100 mt-2">
                    <div class="d-flex flex-column px-3 w-100">
                      <input
                        id="signatureOutCarRentalName"
                        formControlName="signatureOutCarRentalName"
                        class="form-control mb-1"
                        placeholder="Enter name of representative"
                        style="flex: 1"
                      />
                      <!-- Validation error for signatureOutCarRentalName -->
                      <div
                        class="text-danger mt-1 fs-small"
                        *ngIf="
                          vehicleBookingForm.get('signatureOutCarRentalName')?.invalid &&
                          hasSubmitted()
                        "
                      >
                        <div
                          *ngIf="
                            vehicleBookingForm
                              .get('signatureOutCarRentalName')
                              ?.hasError('required')
                          "
                        >
                          Representative name is required
                        </div>
                      </div>
                      <span>{{ today | date }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <br />
              <br />
              <div
                class="row d-flex flex-column align-items-center justify-content-center mt-4 gap-3"
              >
                <p class="p-3">
                  Vehicle Provider agrees that the above report is a true record of the received
                  condition and has understood
                  <a href="#" class="text-primary">the vehicle handover agreement terms.</a>
                  Vehicle hirer agrees that the above report is a true record of the received
                  condition and has understood
                  <a href="#" class="text-primary">the vehicle handover agreement terms.</a>
                </p>

                <div class="p-3 w-100">
                  <div class="alert alert-info ms-3 d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <p class="m-0">
                      <span *ngIf="getTotalMileageAllowance() === 0">
                        The daily mileage limit for this vehicle is
                        <strong>Unlimited</strong>.
                      </span>
                      <span *ngIf="getTotalMileageAllowance() !== 0">
                        The daily mileage limit for this vehicle is
                        <strong>{{ getTotalMileageAllowance() }} KM/day</strong>
                        <span
                          *ngIf="log.promotion?.promotionType === 'EXTRA_MILEAGE'"
                          class="text-success"
                        >
                          (includes {{ log.promotion.extraMileage }} KM/day bonus from promotion) </span
                        >. An excess mileage rate of
                        <strong
                          >{{ log.vehicle.excessMileageRate | contextualCurrency: log }}/KM</strong
                        >
                        applies if this limit is exceeded.
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              <!-- Add the warning alert here -->
              <div class="col-12 mb-3 p-0" *ngIf="!getDocumentByType('DRIVER')">
                <div
                  class="alert alert-warning d-flex align-items-center justify-content-between"
                  role="alert"
                >
                  <div>
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Client's driving license has not been uploaded. This document is required for
                    vehicle handover.
                  </div>
                  <button type="button" class="btn btn-warning btn-sm" (click)="viewHirerDetails()">
                    Upload Documents
                  </button>
                </div>
              </div>

              <div class="card mt-2">
                <div class="card-body text-center">
                  <div class="d-flex justify-content-center align-items-center">
                    <input
                      type="checkbox"
                      class="m-0"
                      id="acknowledgeTerms"
                      name="acknowledgeTerms"
                      formControlName="acknowledgeTerms"
                    />
                    <label for="acknowledgeTerms" class="m-0 pl-1">
                      By dispatching, I acknowledge that the customer has signed and agreed to the
                      terms and conditions
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Trigger Button -->
          <div *ngIf="log.status === 'BOOKED'">
            <div class="text-center mb-2">
              <button
                type="submit"
                class="btn btn-primary btn-lg px-5 mx-1 py-2"
                [disabled]="!vehicleBookingForm.get('acknowledgeTerms')?.value || isSubmitting()"
              >
                @if (isSubmitting()) {
                  <span
                    class="spinner-border spinner-border-sm me-2"
                    role="status"
                    aria-hidden="true"
                  ></span>
                  Submitting...
                } @else {
                  Dispatch
                }
              </button>
            </div>
          </div>
        </div>
      </div>
    </form>

    <div class="container mt-5">
      <div class="card">
        <div class="card-header">
          <div
            class="bg-custom-danger text-white p-2 d-flex justify-content-between align-items-center"
            style="cursor: pointer"
            (click)="hirerInfoCollapsed = !hirerInfoCollapsed"
          >
            <strong>Important Hirer Information - Please Read Carefully</strong>
            <i class="fas" [ngClass]="hirerInfoCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
          </div>
        </div>
        <div class="card-body" [ngClass]="{ 'd-none': hirerInfoCollapsed }">
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">During your hire</h6>

            <p>
              In the event of a road traffic accident, or any incident of vehicle damage, you must
              call our Emergency Support Line on
              <strong>+************ </strong> <strong>IMMEDIATELY</strong>. Reporting any accident
              within 2 hours of incident is a contractual requirement and failure to do so may
              result in increased hirer costs and forfeiture of deposit.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Breakdown / Mechanical issues</h6>
            <p>
              If you experience a breakdown call you should follow guidelines from the vehicle
              provider if they have recommended roadside assistance, or if you have your own cover,
              you can inform the vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Recovery</h6>
            <p>
              Should the hire vehicle need recovering the hirer is liable for costs, if recovery is
              needed as a result of an accident, negligence or driver misuse.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Onward Travel Following Breakdown</h6>
            <p>
              In the even/t of vehicle recovery, immediate onward travel costs are not covered.
              Unless offered as courtesy service by vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Replacement Vehicle Following Breakdown</h6>
            <p>
              In the event of vehicle recovery, the vehicle provider may offer a replacement
              vehicle. If they cannot, they should reimburse the balance of the remaining rental
              period. Contact support&#64;mykarlink.com.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Lost Key</h6>
            <p>
              In the vehicle key is lost during hire, hirers are liable for a lost key charge as
              specified by the vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Fuel</h6>
            <p>
              Please check the fuel type before filling. If you mis-fuel the vehicle you will be
              liable for all costs incurred in fixing the vehicle.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Rental Extension / Branch Contact</h6>
            <p>
              To request an extension contact the vehicle provider directly. Karlink terms and
              conditions will not be applicable for any extensions granted outside the scope of
              Karlink service provisions.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Condition Inspection</h6>
            <p>
              1) The vehicle provider should inspect the vehicle before making it available to hire.
              2) The hirer must perform their own inspection to satisfy themselves with condition /
              condition report (VCR) before driving. Please take photographs/video of any damage, or
              anything you are unsure of and email support&#64;mykarlink.com and also copy the
              vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Damage Tolerance</h6>
            <p>
              Not all damage is chargeable and only chargeable damage is recorded to the VCR.
              Bodywork scratches 40mm+ are chargeable, including combined accumulation of scratches
              deemed to be from the same incident. Scratches below 40mm are not chargeable. Bodywork
              dents 20mm+ are chargeable. Dents below 20mm are not chargeable. ANY damage to wheels
              or tyres is chargeable. Glass chips greater than 1mm are chargeable. In the case of
              multiple damages to a car then charges are ‘per occurrence’. Exclusions from the
              excess exist. Changes in condition to wheels, tyres, glass, roof, underneath, clutch &
              interior, or any change in condition attributed to negligence or misuse, are not
              limited by your
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Damage Tolerance</h6>
            <p>
              Not all damage is chargeable and only chargeable damage is recorded to the VCR.
              Bodywork scratches 40mm+ are chargeable, including combined accumulation of scratches
              deemed to be from the same incident. Scratches below 40mm are not chargeable. Bodywork
              dents 20mm+ are chargeable. Dents below 20mm are not chargeable. ANY damage to wheels
              or tyres is chargeable. Glass chips greater than 1mm are chargeable. In the case of
              multiple damages to a car then charges are ‘per occurrence’. Exclusions from the
              excess exist. Changes in condition to wheels, tyres, glass, roof, underneath, clutch &
              interior, or any change in condition attributed to negligence or misuse, are not
              limited by your excess liability.
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Glass and Tyre Damage</h6>
            <p>
              In the event of windscreen or tyre damage during your rental, preventing safe driving,
              the hirer is responsible for replacement and payment of these items. Any replacement
              items must be ‘like for like’, meaning the new product must be at least the same
              standard as the one it replaces. This is especially applicable to tyres; they must be
              replaced with the same brand and version.
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Personal Belongings / Lost property</h6>
            <p>The vehicle provider is not responsible for hirer items left in vehicles.</p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Returns</h6>
            <p>
              You must ensure your vehicle is returned to the specific vehicle provider agreed
              address. Do NOT give keys or a vehicle to anyone other the issuer or their designated
              staff member.
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Smoking in the vehicle / Animals / Car cleanliness</h6>
            <p>Smoking is not permitted in vehicles hired via Karlink.</p>
            <p>
              Hirers are not permitted to allow animals in vehicles hired via Karlink unless agreed
              with vehicle provider.
            </p>
            <p>
              Please return the car in fair condition removing all personal belongings and waste.
            </p>
            <p>
              The vehicle provider may deduct $50 from your deposit for vehicles returned in
              unreasonable condition, such as stained seats, footprints on seats, sand in the
              interior, discarded food or excess dirt.
            </p>
            <p>
              Parking Charge Notices / Traffic Violations / Management Fee are the responsibility of
              the hirer.
            </p>
          </div>
        </div>
        <div class="card-header">
          <div
            class="bg-custom-danger text-white p-2 d-flex justify-content-between align-items-center"
            style="cursor: pointer"
            (click)="termsCollapsed = !termsCollapsed"
          >
            <strong>Hire Terms and conditions</strong>
            <i class="fas" [ngClass]="termsCollapsed ? 'fa-chevron-down' : 'fa-chevron-up'"></i>
          </div>
        </div>

        <div class="card-body" [ngClass]="{ 'd-none': termsCollapsed }">
          <div class="mb-4">
            <p>
              When you sign the rental agreement you accept the conditions below. ‘Provider’ is the
              company/individual referred to as the lessor in the rental agreement and ‘You’ are
              either the company or person referred to as hirer and/or the driver in the rental
              agreement.
            </p>
            <p>
              Please read this agreement carefully. If there is anything you do not understand or
              disagree with, please contact Karlink via support&#64;mykarlink.com.
            </p>
            <br />
            <br />
            <h6>1. Rental Period</h6>
            <p>
              You hire the vehicle for the rental period shown in the agreement. Lessor may agree to
              extend this rental period. Karlink advises such extensions to be processed via Karlink
              website to continue to be covered by the terms and conditions herein.
            </p>
            <p>
              Lessor is entitled to terminate this agreement if you break any of its terms and you
              must then return the vehicle immediately, failure to do so may be reported as theft.
            </p>
            <h6>2. Your Responsiblities</h6>
            <p>
              a) You must look after the vehicle and its keys. You must always lock the vehicle when
              you are not using it, and use any security device supplied with the vehicle. You are
              uninsured in respect of and must personally indemnify the Company for all costs and
              losses, including, but not limited to, recovery costs, losses or repair costs, caused
              by the following matters, for which you are hereby responsible:
            </p>
            <p>b) failure to return keys to the lessor on time or at all;</p>
            <p>
              c) bad weather, as you must always protect the vehicle against weather severe enough
              to cause vehicle damage;
            </p>

            <p>d) failure to use the correct fuel;</p>
            <p>e) failure to maintain correct vehicle fluid levels and tyre pressures;</p>
            <p>
              f) hitting low-level objects, including low tree branches or height-restriction
              devices;
            </p>
            <p>
              g) failure to return the vehicle in the condition in which it was at its pre-rental
              inspection and damage to the exterior and interior of it while in your possession;
              provided that if the damage is covered by insurance, you shall be required to pay the
              cost to the lessor of repair up to the amount of the deposit only.
            </p>
            <p>
              h) loss or damage to equipment supplied (whether hired to you or supplied free of
              charge) at the time of rental, including, but not limited to, satellite navigation
              equipment, baby seats and other goods separate from the vehicle itself.
            </p>
            <p>
              (i) You must not sell, rent, lend or dispose of the whole vehicle or any of its parts.
            </p>
            <p>(ii) You must not give anyone any legal rights over the vehicle.</p>
            <p>
              (iii)You must not let anyone work on the vehicle without permission of the lessor. If
              given permission, you will be responsible for the cost of the work.
            </p>

            <p>
              (iv) You must inform the lessor of any fault in the vehicle as soon as you notice it.
            </p>
            <p>
              (v) You must return the vehicle to the place specified in the rental agreement, at the
              agreed times. You must ensure the lessor inspects the vehicle to check its condition
              on return, and you remain responsible for the vehicle until it has been so checked.
            </p>
            <p>
              (vi) You must return the vehicle empty of all goods and things which you and others
              put into it.
            </p>
            <p>
              (vii) You must not allow any unauthorised person to drive the vehicle. Authorised
              persons are those named on this agreement or authorised by the lessor in writing, or
              corporate hirers who have satisfied the terms of rental that they have suitable
              insurance covering accidents by their staff whom they authorise to drive the vehicle.
            </p>
            <p>
              (viii) You are responsible for all costs incurred through your negligence, in the
              event that the Insurer voids the insurance cover or refuses to indemnify you for any
              reason: for example, damage to clutch.
            </p>
            <p>
              (ix) You must pay the appropriate authority any fines and costs if and when the
              authority demands this payment.
            </p>

            <h6>3. Vehicle provider’s responsibilities.</h6>
            <p>
              The lessor warrant that they have maintained the vehicle to at least the
              manufacturer’s recommended standard, and that it is roadworthy and suitable for
              renting at the start of the rental period. The lessor is responsible to ensure that
              the vehicle is fit to drive; and we has the right to rent it out. Lessor is
              responsible if someone is injured or dies as a result of negligent act or failure to
              act. Lessor is also responsible for losses caused by them breaking this agreement, but
              only such losses as are a foreseeable consequence of them breaking the agreement.
              Losses are foreseeable where they could be contemplated by you and the lessor at the
              time the vehicle is rented.
            </p>
            <p>
              Lessor is not responsible for indirect losses which happen indirectly as a result of
              the main loss or damage and which are not foreseeable by you (such as loss of
              opportunity).
            </p>

            <h6>4. Property.</h6>
            <p>Lessor is not responsible for loss or damage to property left in the vehicle.</p>
            <h6>5. Conditions for using the vehicle.</h6>
            <p>
              The vehicle must only be driven by you and any other driver named in the agreement, or
              by anyone else authorised in writing by the lessor. Anyone driving the vehicle must
              have a full valid driving licence. You or any other authorised driver must not: use
              the vehicle for hire or reward; use the vehicle for any illegal purpose; use the
              vehicle for racing, pace-making, testing the vehicle reliability and speed or taking a
              driving test; use the vehicle while you have alcohol or illegal drugs in your body;
              Use the vehicle while loaded beyond the manufacturer’s maximum weight
            </p>
            <p>
              recommendations; use the vehicle with the load not secured safely; if the vehicle is a
              commercial vehicle, use it for a purpose for which you need an operator licence if you
              do not have one; use the vehicle or allow it to be used off-road or on roads
              unsuitable for it; drive in restricted areas.
            </p>
            <p>
              6. Towing You or any other authorised driver must not use the vehicle for towing
              unless given written permission by the lessor.
            </p>
            <h6>7. Charges.</h6>
            <p>
              (i) The lessor may work out charges using the best quotation from any 3 of their
              choice
            </p>
            <p>(ii) You will pay and indemnify the lessor in respect of the following:</p>
            <p>
              a. The rental and any other charges due under this agreement (unused rental days and
              early returns being non-refundable);
            </p>
            <p>b. Any charge for loss or damage resulting from you breaking condition 2.</p>
            <p>
              c. For fuel consumed and a refuelling charge if you have used, and not replaced, the
              quantity of fuel that was supplied at the start of the original rental.
            </p>
            <p>
              d. The amount of any fines, vehicle clamping costs and court costs for parking
              violations, and other traffic violations;
            </p>
            <p>
              e. KarLink may charge management fees for dealing with any issues in respect of the
              matters in clause 7;
            </p>
            <p>
              f. The reasonable cost of repairing any extra damage which was not noted on the
              vehicle check form at the start of the agreement, whether you were at fault or not
              (subject to clause 3)
            </p>
            <p>
              g. the reasonable cost of replacing the vehicle if stolen or damaged beyond economic
              repair, less any amount recovered from any Insurer under any insurance cover
            </p>

            <p>An excess often excludes specific items, often including damage to</p>
            <p>(i) wheels,</p>
            <p>(ii) tyres,</p>
            <p>
              (iii) glass, which includes headlights, side lights, rear lights and mirror glass;
            </p>
            <p>(iv) the roof and</p>
            <p>(v) the underneath.</p>
            <p>
              h. Any charges arising from any authority seizing the vehicle while in your
              possession, together with a loss-of-income charge while lessor cannot rent out the
              vehicle.
            </p>
            <p>i. Any rates communicated to you for delivering and collecting the vehicle.</p>
            <p>
              j. any fines or costs arising from offences or for any charges demanded by a 3rd party
              as a result of the vehicle being parked or left upon land which is not a public road.
            </p>
            <p>
              m. If for any reason, whether your fault or not, you do not return the vehicle to the
              pre-arranged end of hire location and lessor collects the vehicle then you will pay
            </p>
            <p>(i) all costs for the collection of the vehicle, and</p>
            <p>(ii) compensation for loss of use until collection.</p>
            <h6>8. Lessor should provide FULL COMPREHENSIVE</h6>
            <p>
              insurance and damage protection cover. Lessor may share, but is not obliged to give
              you information about the insurance cover and any restrictions which may apply.
            </p>
            <p>By signing the agreement you are accepting the conditions of the insurance cover.</p>
            <p>
              a. The lessor is required us to have insurance cover for claims arising if you injure
              or kill anybody, or damage property.
            </p>
            <p>b. Lessor insurance cover should provide cover for loss or damage to the vehicle.</p>
            <p>
              c. Lessor insurance cover should provide cover for theft, and damage to the vehicle
              caused during an attempted theft.
            </p>
            <p>
              d. If any person who before the date of the agreement has been convicted of an offence
              relating to driving while having drink or drugs in their body, is driving or in charge
              of the insured vehicle and is proven to the satisfaction of the insurers to be driving
              under the influence of drink or drugs (prescribed or otherwise) at the time of any
              accident, the insurance cover will be limited to that required by the Road Traffic
              Acts.
            </p>
            <h6>9. You may take own insurance cover</h6>
            <p>but this does not replace insurance cover offered by the lessor.</p>
            <p>
              Lessor may ask your insurers to record their name as owners of the vehicle. If the
              vehicle is damaged or stolen you will let the lessor negotiate with the insurers about
              repair of the vehicle and the amount of any payment due. If
            </p>
            <p>
              for any reason the amount which lessor receives from the insurance company is less
              than the amount of any loss suffered, you must pay the difference. You must pay the
              full amount of loss and all costs if the vehicle is damaged, lost or stolen, or a
              claim has been made by any other party, if any insurance policy you have arranged
              fails for any reason whatever.
            </p>
            <h6>10. What to do if you have an accident.</h6>
            <p>
              If you have an accident you Must Not Admit responsibility. You must get the names and
              addresses of everyone involved, including witnesses. You should also make the vehicle
              secure; tell the police straight away if anyone is injured or there is any
              disagreement about who is responsible; and call the lessor from which you rented the
              vehicle within 2 working hours. You must contact Karlink and complete an Accident
              Report Form ("ARF") within 24 hours of the time of the accident.
            </p>
            <p>
              The lessor is not obliged to replace any damaged vehicle with another hire vehicle.
            </p>
            <h6>11. Data Protection You agree that KarLink</h6>
            <p>
              may use any information you have given us to carry out our own market research and you
              authorise us to contact you in the future by the way of direct marketing if we have
              information we feel may be of interest to you. You agree that we can give this
              information to credit reference agencies, debt collectors and any other relevant
              organisation.
            </p>
            <p>
              Motor Insurers share information to prevent fraudulent claims. In the event of a claim
              the information on this form and any claim form may be put on a register and shared
              with others.
            </p>
            <h6>12. Ending the agreement</h6>
            <p>
              a. The lessor or KarLink may end this agreement without prior notice if you break any
              terms of this agreement.
            </p>
            <p>
              b. If you are a company, the lessor or KarLink may end this agreement without notice
              if you go into liquidation; or you call a meeting of creditors, or your goods are
              taken from you to satisfy debts; or you break any of the terms of this agreement.
            </p>
            <p>
              c. If we/the lessor end this agreement, it will not affect our right to claim any
              money we are owed under the agreement. Lessor/KarLink can also claim reasonable costs
              from you if you do not meet any of the terms of this agreement.
            </p>
            <h6>13 Governing law</h6>
            <p>
              This agreement is governed by the laws of the country in which it is signed. Any
              dispute may be settled in its courts.
            </p>
            <p>Last updated 2 January 2025.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #signatureModal let-modal>
  <div
    class="modal-dialog signature-modal-dialog"
    tabindex="-1"
    role="dialog"
    aria-labelledby="signatureModalTitle"
    aria-hidden="false"
  >
    <div class="modal-content signature-modal-content">
      <div class="modal-header bg-main text-white">
        <h5 id="signatureModalTitle" class="modal-title text-center w-100">
          Representative Signature
        </h5>
        <span
          type="button"
          class="close text-white shadow-none"
          (click)="modal.dismiss('Cross click')"
        >
          <span aria-hidden="true">&times;</span>
        </span>
      </div>
      <div class="modal-body">
        <!-- Canvas for signature -->
        <canvas #signatureCanvas class="signature-canvas" width="500" height="200"></canvas>
      </div>
      <div class="modal-footer bg-white">
        <button class="btn btn-danger" (click)="clearSignature()">Clear</button>
        <button type="button" class="btn btn-primary" (click)="saveSignature(modal)">Save</button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #customerSignatureModal let-modal>
  <div
    class="modal-dialog signature-modal-dialog"
    tabindex="-1"
    role="dialog"
    aria-labelledby="customerSignatureModalTitle"
    aria-hidden="false"
  >
    <div class="modal-content signature-modal-content">
      <div class="modal-header bg-main text-white">
        <h5 id="customerSignatureModalTitle" class="modal-title text-center w-100">
          Customer Signature
        </h5>
        <span
          type="button"
          class="close text-white shadow-none"
          (click)="modal.dismiss('Cross click')"
        >
          <span aria-hidden="true">&times;</span>
        </span>
      </div>
      <div class="modal-body">
        <!-- Canvas for signature -->
        <canvas #signatureCanvas class="signature-canvas" width="500" height="200"></canvas>
      </div>
      <div class="modal-footer bg-white">
        <button class="btn btn-danger" (click)="clearSignature()">Clear</button>
        <button type="button" class="btn btn-primary" (click)="saveCustomerSignature(modal)">
          Save
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #editModal let-modal>
  <app-create-vehicle-booking
    [addForm]="vehicleBookingForm"
    [refreshBookings]="refreshBookings()"
  ></app-create-vehicle-booking>
</ng-template>

<ng-template #noData>
  <p>No Vehicle data available.</p>
</ng-template>

<ng-template #hirerDetailsModal let-modal>
  <div class="modal-header bg-main text-white hirer-details-modal-width">
    <h5 class="modal-title text-center w-100">Hirer Details</h5>
    <span type="button" class="close text-white shadow-none" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </span>
  </div>
  <div class="modal-body p-4 hirer-details-modal-width">
    <div class="card mb-4">
      <div class="card-body">
        <div class="hirer-details-container">
          <div class="avatar-column">
            <img
              src="assets/images/placeholder-avatar.png"
              alt="Profile photo"
              class="rounded-circle"
            />
          </div>

          <div class="details-column">
            <div class="contact-info-container">
              <h5 class="mb-2">
                {{ log.client?.name || "Name not provided" }}
              </h5>
              <div class="contact-info-grid">
                <div class="info-item">
                  <i class="fas fa-envelope"></i>
                  <span>{{ log.client?.email || "Email not provided" }}</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-phone"></i>
                  <span>{{ log.client?.telephone || "Phone number not provided" }}</span>
                </div>
              </div>
            </div>

            <div class="additional-info-container">
              <div class="hirer-info-grid">
                <div class="info-item">
                  <i class="fas fa-star text-warning"></i>
                  <span class="mt-1">{{
                    log.client?.rating
                      ? (log.client.rating | number: "1.1-1") + " stars"
                      : "Not rated yet"
                  }}</span>
                </div>
                <div class="info-item">
                  <i
                    class="fas fa-check-circle"
                    [ngClass]="log.client?.verified ? 'text-primary' : 'text-muted'"
                  ></i>
                  <span class="mt-1">{{
                    log.client?.verified ? "Verified Hirer" : "Unverified Hirer"
                  }}</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <span class="mt-1 address-text">{{
                    formatAddress(log.client?.address) || "Address not provided"
                  }}</span>
                </div>
                <div class="info-item">
                  <i class="fas fa-bookmark"></i>
                  <span class="mt-1">{{
                    log.client?.totalBooking
                      ? log.client.totalBooking +
                        (log.client.totalBooking === 1 ? " Booking" : " Bookings")
                      : "No bookings yet"
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="actions-column">
            <button class="btn btn-outline-primary" (click)="openEditAddressModal()">
              Edit Address
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header bg-light">
        <h5 class="mb-0">Documents</h5>
      </div>
      <div class="card-body">
        <div class="row g-4">
          <div class="col-md-6">
            <h6 class="mb-3">Driver's License</h6>
            <div class="document-upload-container">
              @if (getDocumentByType("DRIVER"); as licenseDoc) {
                <div class="d-flex flex-column align-items-center upload-area">
                  <div class="upload-icon mb-2">
                    <i class="fas fa-file-alt fa-2x text-primary"></i>
                  </div>
                  <div class="upload-text text-center">
                    <div class="selected-file mb-2">Driver's License</div>
                    <button
                      (click)="viewUploadedDocument(licenseDoc.url, 'DRIVER')"
                      class="btn btn-link text-primary p-0"
                    >
                      <i class="fas fa-eye me-1"></i>View
                    </button>
                  </div>
                </div>
              } @else if (selectedLicenseFile && selectedLicenseUrl) {
                <div class="d-flex flex-column align-items-center upload-area">
                  <div class="upload-icon mb-2">
                    <i class="fas fa-file-alt fa-2x text-primary"></i>
                  </div>
                  <div class="upload-text text-center">
                    <div class="selected-file mb-2">Driver's License</div>
                    <button
                      (click)="viewUploadedDocument(selectedLicenseUrl, 'DRIVER')"
                      class="btn btn-link text-primary p-0"
                    >
                      <i class="fas fa-eye me-1"></i>View
                    </button>
                  </div>
                </div>
              } @else {
                <div class="document-upload-container">
                  <div
                    class="upload-area"
                    (dragover)="onDragOver($event)"
                    (drop)="onDrop($event, 'LICENCE')"
                  >
                    <input
                      #licenseInput
                      type="file"
                      class="d-none"
                      accept="image/png,image/jpeg,image/jpg,image/webp"
                      (change)="onFileChange($event, 'LICENCE')"
                    />
                    <div class="upload-icon">
                      <i class="fas fa-cloud-upload-alt fa-2x text-muted"></i>
                    </div>
                    <div class="upload-text text-center">
                      <div>Drag and drop document here</div>
                      <small class="text-muted">Supported formats: JPG, PNG, JPEG, WEBM</small>
                    </div>
                  </div>
                  <div class="upload-options mt-3">
                    <button
                      type="button"
                      class="btn btn-outline-primary me-2"
                      (click)="licenseInput.click()"
                    >
                      <i class="fas fa-folder-open me-1"></i>Select File
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      (click)="openCameraModal('LICENCE')"
                    >
                      <i class="fas fa-camera me-1"></i>Take Picture
                    </button>
                  </div>
                </div>
              }
            </div>
          </div>

          <div class="col-md-6">
            <h6 class="mb-3">Proof of residence</h6>
            <div class="document-upload-container">
              @if (getDocumentByType("PROOF_RESIDENCE"); as residenceDoc) {
                <div class="d-flex flex-column align-items-center upload-area">
                  <div class="upload-icon mb-2">
                    <i class="fas fa-file-alt fa-2x text-primary"></i>
                  </div>
                  <div class="upload-text text-center">
                    <div class="selected-file mb-2">Proof of Residence</div>
                    <button
                      (click)="viewUploadedDocument(residenceDoc.url, 'PROOF_RESIDENCE')"
                      class="btn btn-link text-primary p-0"
                    >
                      <i class="fas fa-eye me-1"></i>View
                    </button>
                  </div>
                </div>
              } @else if (selectedResidenceFile && selectedResidenceUrl) {
                <div class="d-flex flex-column align-items-center upload-area">
                  <div class="upload-icon mb-2">
                    <i class="fas fa-file-alt fa-2x text-primary"></i>
                  </div>
                  <div class="upload-text text-center">
                    <div class="selected-file mb-2">Proof of Residence</div>
                    <button
                      (click)="viewUploadedDocument(selectedResidenceUrl, 'PROOF_RESIDENCE')"
                      class="btn btn-link text-primary p-0"
                    >
                      <i class="fas fa-eye me-1"></i>View
                    </button>
                  </div>
                </div>
              } @else {
                <div class="document-upload-container">
                  <div
                    class="upload-area"
                    (dragover)="onDragOver($event)"
                    (drop)="onDrop($event, 'PROOF_OF_RESIDENCE')"
                  >
                    <input
                      #residenceInput
                      type="file"
                      class="d-none"
                      accept="image/png,image/jpeg,image/jpg,image/webp"
                      (change)="onFileChange($event, 'PROOF_OF_RESIDENCE')"
                    />
                    <div class="upload-icon">
                      <i class="fas fa-cloud-upload-alt fa-2x text-muted"></i>
                    </div>
                    <div class="upload-text text-center">
                      <div>Drag and drop document here</div>
                      <small class="text-muted">Supported formats: JPG, PNG, JPEG, WEBM</small>
                    </div>
                  </div>
                  <div class="upload-options mt-3">
                    <button
                      type="button"
                      class="btn btn-outline-primary me-2"
                      (click)="residenceInput.click()"
                    >
                      <i class="fas fa-folder-open me-1"></i>Select File
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      (click)="openCameraModal('PROOF_OF_RESIDENCE')"
                    >
                      <i class="fas fa-camera me-1"></i>Take Picture
                    </button>
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #editAddressModal let-modal>
  <div class="modal-header bg-main text-white">
    <h5 class="modal-title">Edit Address</h5>
    <span type="button" class="close text-white shadow-none" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </span>
  </div>
  <div class="modal-body p-4">
    <form [formGroup]="addressForm" (ngSubmit)="updateAddress()">
      <div class="mb-3">
        <label for="addressLine" class="form-label">Address Line*</label>
        <input
          type="text"
          class="form-control"
          id="addressLine"
          formControlName="firstLine"
          [class.is-invalid]="
            addressForm.get('firstLine')?.invalid && addressForm.get('firstLine')?.touched
          "
        />
        <div class="invalid-feedback" *ngIf="addressForm.get('firstLine')?.errors?.['required']">
          Address line is required
        </div>
      </div>

      <div class="mb-3">
        <label for="city" class="form-label">City*</label>
        <input
          type="text"
          class="form-control"
          id="city"
          formControlName="town"
          [class.is-invalid]="addressForm.get('town')?.invalid && addressForm.get('town')?.touched"
        />
        <div class="invalid-feedback" *ngIf="addressForm.get('town')?.errors?.['required']">
          City is required
        </div>
      </div>

      <div class="mb-3">
        <label for="county" class="form-label">Country*</label>
        <!-- Use the country-selector component as a form control -->
        <country-selector
          id="county"
          formControlName="county"
          [class.is-invalid]="
            addressForm.get('county')?.invalid && addressForm.get('county')?.touched
          "
        ></country-selector>
        <div class="invalid-feedback" *ngIf="addressForm.get('county')?.errors?.['required']">
          Country is required
        </div>
      </div>

      <div class="d-flex justify-content-end">
        <button
          type="button"
          class="btn btn-secondary me-2"
          (click)="modal.dismiss()"
          [disabled]="isUpdatingAddress()"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="btn btn-primary"
          [disabled]="addressForm.invalid || isUpdatingAddress()"
        >
          @if (isUpdatingAddress()) {
            <span
              class="spinner-border spinner-border-sm me-2"
              role="status"
              aria-hidden="true"
            ></span>
            Updating...
          } @else {
            Update Address
          }
        </button>
      </div>
    </form>
  </div>
</ng-template>

<!-- Camera Modal -->
<ng-template #cameraModal let-modal>
  <div class="modal-header camera-modal-width bg-main text-white">
    <h5 class="modal-title">
      <i class="fas fa-camera me-2"></i>
      Take Picture for
      {{ currentDocumentType === "LICENCE" ? "Driver's License" : "Proof of Residence" }}
    </h5>
    <span type="button" class="close text-white shadow-none" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </span>
  </div>

  <div class="modal-body p-4 camera-modal-width">
    @if (!capturedImageDataUrl && cameraActive) {
      <!-- Camera View -->
      <div class="camera-container mb-3">
        <webcam
          #webcamRef
          [trigger]="triggerObservable"
          [switchCamera]="switchCameraObservable"
          [width]="640"
          [height]="480"
          [allowCameraSwitch]="true"
          [mirrorImage]="'auto'"
          [imageType]="'image/jpeg'"
          [imageQuality]="0.8"
          (imageCapture)="onImageCaptured($event)"
          (initError)="onCameraInitError($event)"
          (cameraSwitched)="onCameraSuccess()"
          class="w-100"
          style="max-height: 400px; object-fit: cover"
        >
        </webcam>
      </div>

      <!-- Camera Controls -->
      <div class="d-flex justify-content-center gap-2">
        <button type="button" class="btn btn-primary btn-lg mr-2" (click)="captureImage()">
          <i class="fas fa-camera mr-2"></i>Capture
        </button>
        <button type="button" class="btn btn-secondary mr-2" (click)="switchCameraDevice()">
          <i class="fas fa-sync-alt mr-2"></i>Switch Camera
        </button>
        <button type="button" class="btn btn-outline-secondary mr-2" (click)="closeCameraModal()">
          <i class="fas fa-times mr-2"></i>Cancel
        </button>
      </div>
    } @else if (!capturedImageDataUrl && !cameraActive) {
      <!-- Camera Loading State -->
      <div class="d-flex justify-content-center align-items-center" style="min-height: 300px">
        <div class="text-center">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p>Initializing camera...</p>
        </div>
      </div>
    } @else {
      <!-- Captured Image Preview -->
      <div class="captured-image-container mb-3">
        <h6 class="mb-2">Captured Image Preview:</h6>
        <div class="text-center">
          <img
            [src]="capturedImageDataUrl"
            alt="Captured image"
            class="img-fluid border rounded"
            style="max-height: 400px; max-width: 100%"
          />
        </div>
      </div>

      <!-- Preview Controls -->
      <div class="d-flex justify-content-center gap-2">
        <button type="button" class="btn btn-success btn-lg mr-2" (click)="confirmCapturedImage()">
          <i class="fas fa-check mr-2"></i>Use This Image
        </button>
        <button
          type="button"
          class="btn btn-warning mr-2"
          (click)="capturedImageDataUrl = null; webcamImage = null; cameraActive = true"
        >
          <i class="fas fa-redo mr-2"></i>Take Another
        </button>
        <button type="button" class="btn btn-outline-secondary mr-2" (click)="closeCameraModal()">
          <i class="fas fa-times mr-2"></i>Cancel
        </button>
      </div>
    }
  </div>
</ng-template>
