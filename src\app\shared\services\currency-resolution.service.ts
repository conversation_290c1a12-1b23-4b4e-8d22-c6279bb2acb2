import { Injectable, inject } from "@angular/core";
import { CurrencyService } from "./currency.service";
import { AuthService } from "./auth.service";

@Injectable({
  providedIn: "root",
})
export class CurrencyResolutionService {
  private currencyService = inject(CurrencyService);
  private authService = inject(AuthService);

  constructor() {}

  /**
   * Determines which currency to use based on user type and context
   * @param userType - The type of user ('admin', 'agency', etc.)
   * @param dataItem - Optional data item (vehicle, booking, or invoice) containing currency information
   * @returns The currency code to use
   */
  getCurrencyForContext(userType: string, dataItem?: any): string {
    if (userType === "admin" && dataItem) {
      // For admin users, detect the data type and extract currency accordingly

      // Check if it's a booking (has vehicle.agency property)
      if (dataItem.vehicle?.agency?.baseCurrency) {
        return dataItem.vehicle.agency.baseCurrency;
      }

      // Check if it's a vehicle or invoice (both have agency property directly)
      if (dataItem.agency?.baseCurrency) {
        return dataItem.agency.baseCurrency;
      }
    }

    // For provider users or when admin has no data context, use base currency
    return this.currencyService.getBaseCurrency() || this.getDefaultCurrency();
  }

  /**
   * Extract currency from booking object
   * @param booking - The booking object
   * @returns The currency code or null if not found
   */
  getCurrencyFromBooking(booking: any): string | null {
    if (booking?.vehicle?.agency?.baseCurrency) {
      return booking.vehicle.agency.baseCurrency;
    }
    return null;
  }

  /**
   * Extract currency from vehicle object
   * @param vehicle - The vehicle object
   * @returns The currency code or null if not found
   */
  getCurrencyFromVehicle(vehicle: any): string | null {
    if (vehicle?.agency?.baseCurrency) {
      return vehicle.agency.baseCurrency;
    }
    return null;
  }

  /**
   * Extract currency from invoice object
   * @param invoice - The invoice object
   * @returns The currency code or null if not found
   */
  getCurrencyFromInvoice(invoice: any): string | null {
    if (invoice?.agency?.baseCurrency) {
      return invoice.agency.baseCurrency;
    }
    return null;
  }

  /**
   * Get the default fallback currency
   * @returns The default currency code
   */
  getDefaultCurrency(): string {
    return "USD";
  }

  /**
   * Get current user type
   * @returns The current user type
   */
  getCurrentUserType(): string {
    return this.authService.getUserType();
  }

  /**
   * Check if the current user is an admin
   * @returns True if user is admin, false otherwise
   */
  isAdminUser(): boolean {
    return this.getCurrentUserType() === "admin";
  }

  /**
   * Check if the current user is a provider/agency
   * @returns True if user is a provider, false otherwise
   */
  isProviderUser(): boolean {
    return this.getCurrentUserType() === "agency";
  }

  /**
   * Get the currency text to display in table headers
   * For providers: Show their base currency (e.g., "(GBP)")
   * For admins: Show empty string since each row may have different currencies
   * @returns The header currency text
   */
  getHeaderCurrency(formatted = true, dataItem?: any): string {
    let currency = "";

    if (this.isAdminUser()) {
      if (dataItem) {
        currency = this.getCurrencyForContext("admin", dataItem);
      }
    } else {
      currency = this.currencyService.getBaseCurrency() || "USD";
    }

    // empty string
    if (!currency) {
      return currency;
    }

    return formatted ? `(${currency})` : `${currency}`;
  }
}
