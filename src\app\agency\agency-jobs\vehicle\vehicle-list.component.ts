import { AuthService } from "./../../../shared/services/auth.service";
import {
  Component,
  <PERSON>Init,
  On<PERSON><PERSON>roy,
  TemplateRef,
  computed,
  effect,
  inject,
  resource,
  signal,
} from "@angular/core";
import {
  FormArray,
  FormGroup,
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import { NgbModal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import { ToastrService } from "ngx-toastr";
import { Subscription, lastValueFrom, Subject } from "rxjs";
import { debounceTime, distinctUntilChanged, takeUntil } from "rxjs/operators";
import { AgencyService } from "src/app/shared/services/agency.service";
import { CurrencyService } from "src/app/shared/services/currency.service";
import { AssetService } from "src/app/shared/services/asset.service";
import { ShiftService } from "src/app/shared/services/shift.service";
import { StorageService } from "src/app/shared/services/storage.service";
import { VehicleAvailabilityService } from "src/app/shared/services/vehicle-availability.service";
import { VehicleAvailabilityModalComponent } from "src/app/shared/components/vehicle-availability-modal/vehicle-availability-modal.component";
import { environment } from "src/environments/environment";
import { Page } from "src/app/shared/models/page.model";
import { ActivatedRoute, Router } from "@angular/router";

// jQuery is not used in this component

@Component({
  selector: "app-vehicle-list",
  templateUrl: "./vehicle-list.component.html",
  styleUrls: ["./vehicle-list.component.css"],
  standalone: false,
})
export class VehicleListComponent implements OnInit, OnDestroy {
  // Inject services
  private shiftsService = inject(ShiftService);
  private toast = inject(ToastrService);
  private modalService = inject(NgbModal);
  private assetService = inject(AssetService);
  private storageService = inject(StorageService);
  private vehicleAvailabilityService = inject(VehicleAvailabilityService);
  private fb = inject(UntypedFormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private agencyService = inject(AgencyService);
  private currencyService = inject(CurrencyService);

  private destroy$ = new Subject<void>();
  private searchMakeSubject = new Subject<string>();

  // Filter state as signals
  searchMake = signal("");
  searchRegno = signal("");
  searchStatus = signal("");
  searchModel = signal("");
  searchLocation = signal("");
  searchProvider = signal("");

  // For backward compatibility with existing code
  get searchTerm() {
    return {
      make: this.searchMake(),
      regno: this.searchRegno(),
      status: this.searchStatus(),
      model: this.searchModel(),
      location: {
        city: this.searchLocation(),
      },
      provider: this.searchProvider(),
    };
  }

  // Set search term properties with signals
  set searchTerm(value: any) {
    if (value.make !== undefined) this.searchMake.set(value.make);
    if (value.regno !== undefined) this.searchRegno.set(value.regno);
    if (value.status !== undefined) this.searchStatus.set(value.status);
    if (value.model !== undefined) this.searchModel.set(value.model);
    if (value.location?.city !== undefined) this.searchLocation.set(value.location.city);
    if (value.provider !== undefined) this.searchProvider.set(value.provider);
  }

  isRental = environment.isCarRental;

  // UI state as signals
  selectedInventory = signal<any>(null); // Store selected inventory item for modal
  selectedVehiclePromotions = signal<any[]>([]); // Store selected vehicle's promotions for modal
  selectedVehicle = signal<any>(null);
  selectedDirectorate = signal<any>(null);
  submitted = signal(false);

  warningThreshold = 30;
  baseCurrency: string | null;

  // Pagination state as signals
  pageNumber = signal(0);
  pageSize = signal(20);
  sortBy = signal("");
  directionAsc = signal(true);

  // Computed resource parameters
  resourceParams = computed(() => ({
    agencyId: this.agencyId,
    assetType: "VEHICLE",
    make: this.searchMake(),
    regno: this.searchRegno(),
    status: this.searchStatus(),
    model: this.searchModel(),
    location: this.searchLocation(),
    provider: this.searchProvider(),
    page: this.pageNumber(),
    size: this.pageSize(),
  }));

  // Define the type for our resource parameters
  private resourceParamsType = {
    agencyId: "",
    assetType: "VEHICLE",
    make: "",
    regno: "",
    status: "",
    model: "",
    location: "",
    provider: "",
    page: 0,
    size: 20,
    sort: "",
    direction: true,
  };

  // Resource for vehicles
  vehiclesResource = resource<Page<any>, typeof this.resourceParamsType>({
    request: () => ({
      agencyId: this.agencyId,
      assetType: "VEHICLE",
      make: this.searchMake(),
      regno: this.searchRegno(),
      status: this.searchStatus(),
      model: this.searchModel(),
      location: this.searchLocation(),
      provider: this.searchProvider(),
      page: this.pageNumber(),
      size: this.pageSize(),
      sort: this.sortBy(),
      direction: this.directionAsc(),
    }),
    loader: async ({ request }) => {
      // Build filter parameters for the API call
      let filterParams = "";
      if (request.make) filterParams += `make:${request.make},`;
      // Remove trailing comma if exists
      if (filterParams.endsWith(",")) {
        filterParams = filterParams.slice(0, -1);
      }

      return await lastValueFrom(
        this.assetService.getAgencyAssets(
          request.agencyId,
          request.status,
          request.make,
          request.page,
          request.size,
          request.sort,
          request.direction,
        ),
      );
    },
  });

  // Computed properties for UI
  vehicles = computed(() => {
    return this.vehiclesResource.value()?.content || [];
  });

  // Use the server-filtered vehicles directly
  filteredVehicles = computed(() => {
    return this.vehicles();
  });

  totalItems = computed(() => this.vehiclesResource.value()?.totalElements || 0);
  first = computed(() => this.vehiclesResource.value()?.first || false);
  last = computed(() => this.vehiclesResource.value()?.last || false);
  showin = computed(() => {
    const total = this.totalItems();
    const page = this.pageNumber();
    const size = this.pageSize();
    return (page + 1) * size > total ? total : (page + 1) * size;
  });

  locationFilter = signal<number | null>(null);

  addForm: FormGroup;
  private maxDailyMileage$ = new Subscription();

  editForm: FormGroup;

  locationId = signal<number | null>(null);
  agencyId: string;
  agency: any;
  currencySymbol: string = "$"; // Default fallback
  shareLink = signal("");
  userType: string;
  clients: any;
  isCarRental = environment.isCarRental;
  maxvehicleAddons = 6; // Limit the number of add-ons

  carMakes = ["Toyota", "Honda", "Nissan", "Mercedes-Benz", "BMW", "VolksWagen", "Lexus"];

  selectedDoor: string = "";
  selectedSeat: string = "";
  agencyLocations: any;

  constructor() {
    this.userType = this.authService.getUserType();
    this.agencyId = this.storageService.decrypt(localStorage.getItem("agentId"));
    this.baseCurrency = this.currencyService.getBaseCurrency();

    // Initialize sortBy with a default value
    this.sortBy.set("name");

    // Set up effect to refresh vehicles when filters change
    effect(() => {
      // This will run whenever any of the dependencies change
      console.log("Filters changed, refreshing vehicles");
      console.log("Filters:", this.searchTerm);
      console.log("Page:", this.pageNumber());
      console.log("Sort:", this.sortBy(), this.directionAsc());

      // Update URL with current state
      this.updateUrlWithState();
    });
  }

  ngOnInit(): void {
    // Setup debounced search for make
    this.searchMakeSubject
      .pipe(
        debounceTime(400), // Wait 400ms after last keystroke
        distinctUntilChanged(),
        takeUntil(this.destroy$),
      )
      .subscribe((term) => {
        this.searchMake.set(term);
        this.resetPageNumber();
        // Don't auto-apply filter here to allow for Enter key press
      });

    // Get state from URL query parameters if available
    this.route.queryParams.subscribe((params) => {
      // Only set values if they exist in the URL
      if (params["page"] !== undefined) this.pageNumber.set(Number(params["page"]));
      if (params["size"] !== undefined) this.pageSize.set(Number(params["size"]));
      if (params["sortBy"] !== undefined) this.sortBy.set(params["sortBy"]);
      if (params["directionAsc"] !== undefined)
        this.directionAsc.set(params["directionAsc"] === "true");

      // Set filter values if they exist
      if (params["make"] !== undefined) this.searchMake.set(params["make"]);
      if (params["regno"] !== undefined) this.searchRegno.set(params["regno"]);
      if (params["status"] !== undefined) this.searchStatus.set(params["status"]);
      if (params["model"] !== undefined) this.searchModel.set(params["model"]);
      if (params["location"] !== undefined) this.searchLocation.set(params["location"]);
      if (params["provider"] !== undefined) this.searchProvider.set(params["provider"]);
    });

    // Initial load of vehicles will happen automatically through the resource
    // because we're using signals that trigger the resource to reload
    this.getLocations();

    this.addAddOn(); // Add initial input field

    this.editForm = this.fb.group({
      id: 0,
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();
  }

  getBasicCurrencySymbol(currencyCode: string): string {
    const symbolMap: { [key: string]: string } = {
      USD: "$",
      EUR: "€",
      GBP: "£",
      CAD: "C$",
      AUD: "A$",
      JPY: "¥",
      CHF: "CHF",
      SEK: "kr",
      NOK: "kr",
      DKK: "kr",
      PLN: "zł",
    };
    return symbolMap[currencyCode] || currencyCode;
  }

  onRateInput(event: any, index: number) {
    let rawValue = event.target.value.replace(/\D/g, "");
    const control = this.ratesArray.at(index);
    control.get("rate")?.setValue(rawValue);
  }

  onSearchMakeChange(term: string): void {
    this.searchMakeSubject.next(term);
  }

  get vehicleAddons(): FormArray {
    return (this.addForm?.get("vehicleAddons") ?? []) as FormArray;
  }

  // Method to create a new add-on form group
  createAddOn(): FormGroup {
    return this.fb.group({
      name: ["", Validators.required], // Add-on name
      price: ["", [Validators.required, Validators.min(0)]], // Add-on price
    });
  }

  // Add a new add-on item
  addAddOn(): void {
    if (this.vehicleAddons?.length < this.maxvehicleAddons) {
      this.vehicleAddons?.push(this.createAddOn());
    }
  }

  // Remove an add-on item by index
  removeAddOn(index: number): void {
    this.vehicleAddons?.removeAt(index);
  }

  onDoorSelected(door: string) {
    console.log("Selected Door:", door);
    this.selectedDoor = door;
  }
  onSeatSelected(seat: string) {
    console.log("Selected Seat:", seat);
    this.selectedSeat = seat;
  }

  formInit(modal: any) {
    this.addForm = this.fb.group({
      agency: this.fb.group({
        id: [this.storageService.decrypt(localStorage.getItem("agentId")), Validators.required],
      }),
      name: ["", Validators.required],
      notes: [""],
      status: ["AVAILABLE"],
      stock: [""],
      model: ["", Validators.required],
      regno: [""],
      version: [""],
      color: [""],
      vehicleAddons: this.fb.array([]),
    });

    this.openModal(modal);
  }

  formInitRental(modal: any) {
    this.addForm = this.fb.group({
      agency: this.fb.group({
        id: [this.storageService.decrypt(localStorage.getItem("agentId")), Validators.required],
      }),
      vehicleAddons: this.fb.array([]),
      description: ["", [Validators.maxLength(160)]],
      name: ["", Validators.required],
      notes: [""],
      status: ["AVAILABLE"],
      stock: [""],
      model: ["", Validators.required],
      regno: ["", Validators.required],
      version: [""],
      color: ["", Validators.required],
      capacity: [""],

      carType: [""],
      doors: ["", Validators.required],
      engineNumber: [""],
      engineSize: ["", Validators.required],
      forRental: [true],
      fuelType: ["", Validators.required],
      locationIds: [[], Validators.required],
      mainPhoto: [""],
      maxAge: [""],
      depositAmt: ["0"],
      minHireDays: ["1"],
      airConditioning: [false],
      maxDailyMileage: ["0", Validators.required],
      excessMileageRate: ["0", Validators.required],
      minAge: [""],
      seats: ["", Validators.required],
      trackerId: [""],
      transmissionType: ["", Validators.required],
      type: ["", Validators.required],
      vehicleRates: this.fb.array([
        this.fb.group({
          rate: ["", Validators.required],
          weekDay: ["MTF"],
          desc: ["Mon - Fri"],
        }),
        this.fb.group({
          rate: ["", Validators.required],
          weekDay: ["SUN"],
          desc: ["Sun"],
        }),
        this.fb.group({
          rate: ["", Validators.required],
          weekDay: ["SAT"],
          desc: ["Sat"],
        }),
      ]),
    });

    // If maxDailyMileage changes to '' (value for Unlimited), set the excess charge to 0
    const maxDailyMileageControl = this.addForm.get("maxDailyMileage");
    if (maxDailyMileageControl) {
      this.maxDailyMileage$.add(
        maxDailyMileageControl.valueChanges.subscribe((value) => {
          if (value === 0 || value === "0") {
            this.addForm.patchValue({
              excessMileageRate: 0,
            });
          }
        }),
      );
    }

    this.openModal(modal);
  }

  track(_item: any, index: number) {
    return index;
  }

  get ratesArray() {
    return <UntypedFormArray>this.addForm.get("vehicleRates");
  }

  handlePageChange(event: string) {
    if (event === "next" && !this.last()) {
      this.pageNumber.set(this.pageNumber() + 1);
    } else if (event === "prev" && this.pageNumber() > 0) {
      this.pageNumber.set(this.pageNumber() - 1);
    }

    this.getVehicles();
  }

  // Update URL with current state
  updateUrlWithState(): void {
    // Build query params object with current state
    const queryParams: any = {
      page: this.pageNumber(),
      size: this.pageSize(),
      sortBy: this.sortBy(),
      directionAsc: this.directionAsc().toString(),
    };

    // Add filter values if they exist
    if (this.searchMake()) queryParams.make = this.searchMake();
    if (this.searchRegno()) queryParams.regno = this.searchRegno();
    if (this.searchStatus()) queryParams.status = this.searchStatus();
    if (this.searchModel()) queryParams.model = this.searchModel();
    if (this.searchLocation()) queryParams.location = this.searchLocation();
    if (this.searchProvider()) queryParams.provider = this.searchProvider();

    // Update URL without reloading the page
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: queryParams,
      queryParamsHandling: "merge", // Keep existing query params
      replaceUrl: true, // Replace the current URL to avoid adding to browser history
    });
  }

  getVehicles() {
    // Refresh the resource to load vehicles with current filters
    // The resource will automatically use the current signal values
    this.vehiclesResource.reload();

    // Calculate expiry warnings after data is loaded
    if (this.vehicles()) {
      this.calculateExpiryWarnings();
    }
  }

  isInventoryExpiringSoon(inventory: any[]): boolean {
    return inventory?.some((item) => item.isExpiringSoon);
  }

  openInventoryModal(inventoryItem: any, modal: TemplateRef<any>): void {
    this.selectedInventory.set(inventoryItem);
    this.modalService.open(modal, { centered: true });
  }

  // Check if a vehicle has any promotions
  hasPromotions(vehicle: any): boolean {
    return vehicle.promotions && vehicle.promotions.length > 0;
  }

  // Open modal to display vehicle promotions
  openPromotionsModal(vehicle: any, modal: TemplateRef<any>): void {
    this.selectedVehiclePromotions.set(vehicle.promotions || []);
    this.selectedVehicle.set(vehicle);
    this.modalService.open(modal, { centered: true, size: "lg" });
  }

  // Open modal to manage vehicle availability
  openAvailabilityModal(vehicle: any, event: Event): void {
    // Prevent the click from propagating to the row (which would navigate to vehicle details)
    event.stopPropagation();

    const modalRef = this.modalService.open(VehicleAvailabilityModalComponent, {
      centered: true,
      size: "lg",
      backdrop: "static",
    });

    // Pass vehicle data to the modal component
    modalRef.componentInstance.vehicleId = vehicle.id;
    modalRef.componentInstance.vehicleName = `${vehicle.name} ${vehicle.model} (${vehicle.regno})`;

    // Handle modal close/dismiss
    modalRef.result.then(
      (result) => {
        // Modal closed with result
        if (result) {
          this.toast.success("Vehicle availability updated successfully");
          // Refresh the vehicle list to show updated availability status
          this.getVehicles();
        }
      },
      (reason) => {
        // Modal dismissed
        console.log("Modal dismissed", reason);
      },
    );
  }

  calculateExpiryWarnings(): void {
    const today = new Date();
    const vehicles = this.filteredVehicles();

    if (!vehicles) return;

    vehicles.forEach((vehicle: any) => {
      if (vehicle.inventory && vehicle.inventory.length > 0) {
        vehicle.inventory.forEach((item: any) => {
          const nextCheckDate = new Date(item.nextCheckDate);
          const timeDiff = nextCheckDate.getTime() - today.getTime();
          const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));

          // Add warning flags to each inventory item
          item.isExpiringSoon = daysRemaining <= this.warningThreshold;
          item.daysRemaining = daysRemaining;
        });
      }
    });
  }

  applyFilter(): void {
    // Reset to first page when applying filters
    this.pageNumber.set(0);

    // Reload vehicles with new filters
    this.getVehicles();
  }

  clearFilters(): void {
    // Reset all filter signals
    this.searchMake.set("");
    this.searchRegno.set("");
    this.searchStatus.set("");
    this.searchModel.set("");
    this.searchLocation.set("");
    this.searchProvider.set("");

    // Reset to first page
    this.pageNumber.set(0);

    // Clear URL parameters by navigating with empty query params
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        page: 0,
        size: this.pageSize(),
        sortBy: this.sortBy(),
        directionAsc: this.directionAsc().toString(),
      },
      replaceUrl: true,
    });

    // Reload vehicles without filters
    this.getVehicles();
  }

  sort(field: string): void {
    // Reset to first page when sorting
    this.pageNumber.set(0);

    if (this.sortBy() === field) {
      // Toggle sorting direction
      this.directionAsc.set(!this.directionAsc());
    } else {
      // Set new column to sort by
      this.sortBy.set(field);
      this.directionAsc.set(true);
    }

    // Reload vehicles with new sorting
    this.getVehicles();

    // The effect will update the URL automatically
  }

  resetPageNumber(): void {
    this.pageNumber.set(0);
    // The effect will update the URL automatically
  }

  // Helper function to access nested fields like `agency.name`
  getFieldValue(obj: any, field: string): any {
    return field.includes(".") ? field.split(".").reduce((o, i) => o[i], obj) : obj[field];
  }

  // Store locations data
  locations = signal<any[]>([]);

  getLocations() {
    this.shiftsService.getPaginatedAgencyVehicleLocations(0, 1000).subscribe((data) => {
      this.agencyLocations = data;
    });

    this.shiftsService.getPaginatedShiftLocations(0, 1000).subscribe((data) => {
      this.locations.set(data.content);
    });
  }

  openModal(modal: any) {
    this.modalService.open(modal, { centered: true });
  }

  createVehicle(form: UntypedFormGroup) {
    this.submitted.set(true);

    if (form.valid) {
      this.assetService.createAsset(form.value).subscribe({
        next: () => {
          this.modalService.dismissAll();
          this.getVehicles();
          this.toast.success("Vehicle Added Successfully");
        },
      });
    } else {
      // Show specific validation errors for invalid form fields
      this.showFormValidationErrors(form);
    }
  }

  private showFormValidationErrors(form: UntypedFormGroup) {
    const formErrors: string[] = [];

    Object.keys(form.controls).forEach((key) => {
      const control = form.get(key);
      if (control && control.invalid) {
        const fieldName = this.getFieldDisplayName(key);
        if (control.errors?.["required"]) {
          formErrors.push(`${fieldName} is required`);
        } else if (control.errors?.["maxlength"]) {
          formErrors.push(`${fieldName} is too long`);
        } else if (control.errors?.["min"]) {
          formErrors.push(`${fieldName} value is too low`);
        } else if (control.errors?.["max"]) {
          formErrors.push(`${fieldName} value is too high`);
        }
      }
    });

    // Also check FormArray controls (like vehicleRates)
    const vehicleRatesArray = form.get("vehicleRates") as UntypedFormArray;
    if (vehicleRatesArray) {
      vehicleRatesArray.controls.forEach((rateControl, index) => {
        if (rateControl.invalid) {
          const rateGroup = rateControl as UntypedFormGroup;
          const desc = rateGroup.get("desc")?.value || `Rate ${index + 1}`;
          if (rateGroup.get("rate")?.errors?.["required"]) {
            formErrors.push(`${desc} rate is required`);
          }
        }
      });
    }

    this.toast.warning("Please fill in all required fields");
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldDisplayNames: { [key: string]: string } = {
      name: "Body Type",
      model: "Vehicle Model",
      regno: "Registration Number",
      color: "Colour",
      type: "Vehicle Type",
      doors: "Number of Doors",
      seats: "Number of Seats",
      engineNumber: "Engine Number",
      engineSize: "Engine Capacity",
      trackerId: "Tracker ID",
      transmissionType: "Transmission Type",
      fuelType: "Fuel Type",
      locationIds: "Vehicle Locations",
      maxDailyMileage: "Daily Mileage Allowance",
      excessMileageRate: "Excess Mileage Rate",
    };

    return fieldDisplayNames[fieldName] || fieldName;
  }

  updateDirectorate(form: UntypedFormGroup) {
    console.log(form.value);

    if (form.valid) {
      this.shiftsService.updateShiftDirectorate(form.value).subscribe(() => {
        // console.table(resp);
        this.modalService.dismissAll();
        this.getVehicles();
        this.toast.success("Directorate Updated Successfully");
      });
    } else {
      this.toast.warning("Missing Required Info");
    }
  }

  loadDirectorate(ref: string, modal: any, edit: boolean) {
    if (ref) {
      const vehiclesList = this.vehicles();
      const vehicle = vehiclesList.find((r) => r.id == ref);
      if (vehicle) {
        this.selectedDirectorate.set(vehicle);
      }

      if (edit && this.selectedDirectorate()) {
        const directorate = this.selectedDirectorate();
        this.editForm = this.fb.group({
          id: [directorate.id, Validators.required],
          locationId: [this.getLocationId(directorate.location), Validators.required],
          name: [directorate.name, Validators.required],
          postCode: [directorate.postCode, Validators.required],
          phoneNumber: [directorate.phoneNumber, Validators.required],
        });
      }
    }

    if (modal) {
      this.openModal(modal);
    }
  }

  getLocationId(ref: string): number | undefined {
    if (!ref) return undefined;

    const locationsList = this.locations();
    const location = locationsList?.find((r) => r.name === ref);
    return location?.id;
  }

  copyToClipboard(link: string): void {
    navigator.clipboard.writeText(link).then(
      () => {
        this.toast.success("Link copied to clipboard!");
      },
      (_err) => {
        this.toast.error("Failed to copy link!");
      },
    );
  }

  copyDynamicLink(locationId: number, modal: NgbModalRef): void {
    if (locationId != null) {
      const link = `https://mykarlink.com/vehicles/search?location=${locationId}&agencyId=${this.agencyId}`;
      this.shareLink.set(link);
      navigator.clipboard.writeText(link).then(
        () => {
          this.toast.success("Link copied to clipboard!");
        },
        (error) => {
          this.toast.error("Failed to copy link!");
          console.error("Clipboard copy failed:", error);
        },
      );
    } else {
      this.toast.warning("No location selected!");
    }
    this.locationId.set(null); // Reset locationId or any other fields as needed
    // Close the modal
    modal.close();
  }
}
