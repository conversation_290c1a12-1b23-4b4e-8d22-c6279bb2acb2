import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  signal,
  TemplateRef,
  ViewChild,
} from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { CurrencyResolutionService } from "src/app/shared/services/currency-resolution.service";
import { PDFService } from "src/app/shared/services/pdf.service";

@Component({
  selector: "complete-modal",
  templateUrl: "./complete.component.html",
  styleUrls: ["./complete.component.css"],
  standalone: false,
})
export class CompleteComponent implements OnInit, OnChanges {
  @Input() log: any = {}; // Booking log details
  @Output() updated = new EventEmitter<boolean>();
  @ViewChild("signatureCanvas")
  signatureCanvasRef!: ElementRef<HTMLCanvasElement>;
  @Input() checkoutItems: string = ""; // Input from backend

  isGeneratingPDF = signal(false);
  returnForm: FormGroup;
  vehicleForm: FormGroup;
  isSliderDisabled = false; // Default state: editable

  constructor(
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private pdfService: PDFService,
    private toast: ToastrService,
    private currencyResolutionService: CurrencyResolutionService,
  ) {}

  getHeaderCurrency(formatted = true) {
    return this.currencyResolutionService.getHeaderCurrency(formatted, this.log);
  }

  ngOnInit(): void {
    console.log("==== Booking ====", this.log);
    if (!this.returnForm) {
      console.log("Form is not initialized!");
    }
    console.log("Log Data:", this.log);
    if (this.log && this.log.status === "COMPLETE") {
      this.isSliderDisabled = true; // Disable the slider
    }

    this.vehicleForm = this.fb.group({
      fuelOut: [0, [Validators.required, Validators.min(0)]],
      id: [null, Validators.required],
      mileageOut: [null, [Validators.required, Validators.min(0)]],
      signatureOut: ["", Validators.required],
      signatureOutName: ["", Validators.required], // Hirer Name field
      damageInfoOut: this.fb.array([]),
      damageImage: ["", Validators.required],
      checkoutItems: [""], // Use a simple FormControl to store a comma-separated list
      newCommentText: ["", Validators.required], // Add this
      acknowledgeTerms: [false, Validators.requiredTrue],
    });

    this.returnForm = this.fb.group({
      fuelIn: [0, [Validators.required, Validators.min(0)]],
      id: [null, Validators.required],
      mileageIn: [null, [Validators.required, Validators.min(0)]],
      signatureIn: ["", Validators.required],
      signatureInName: ["", Validators.required], // Hirer Name field
      damageInfoIn: this.fb.array([]),
      damageImageIn: ["", Validators.required],
      items: this.fb.array([]), // FormArray for checkout items
      newCommentText: ["", Validators.required],
      acknowledgeTerms: [false, Validators.requiredTrue],
    });

    console.log("Checkout Items from Log:", this.log.checkoutItems);
    // Call patchCheckoutItems if `log.checkoutItems` is available
    if (this.log?.checkoutItems) {
      this.patchCheckoutItems();
    }
  }

  initializeForm(): void {
    this.returnForm = this.fb.group({
      fuelIn: [0, [Validators.required, Validators.min(0)]],
      id: [null, Validators.required],
      mileageIn: [null, [Validators.required, Validators.min(0)]],
      signatureIn: ["", Validators.required],
      signatureInName: ["", Validators.required],
      damageInfoIn: this.fb.array([]),
      damageImageIn: ["", Validators.required],
      items: this.fb.array([]), // Initialize FormArray here
      newCommentText: ["", Validators.required],
      acknowledgeTerms: [false, Validators.requiredTrue],
    });
  }

  ngOnChanges(): void {
    if (this.returnForm && this.log?.checkoutItems) {
      this.patchCheckoutItems();
    }

    if (this.log && this.log.id && this.returnForm) {
      // Patch the form with the values from log
      this.returnForm.patchValue({
        id: this.log.id,
        fuelIn: this.log.fuelIn || 0,
        damageInfoIn: this.log.damageInfoIn,
        mileageIn: this.log.mileageIn || 0,
        signatureIn: this.log.signatureIn || "",
      });

      console.log("Form patched with log data:", this.returnForm.value);
    }

    if (this.log && this.log.id && this.vehicleForm) {
      this.vehicleForm.patchValue({
        fuelOut: this.log.fuelOut,
        id: this.log.id,
        mileageOut: this.log.mileageOut,
        signatureOut: this.log.signatureOut,
        damageInfoOut: this.log.damageInfoOut,
        acknowledgeTerms: [true, Validators.requiredTrue],
      });

      console.log("Form patched with log data:", this.vehicleForm.value);
    }
  }

  handleSubmit() {
    throw new Error("Method not implemented.");
  }

  // Helper methods for signature fallback logic
  getCustomerSignatureOut(): string | null {
    // Check new field first, then fallback to legacy field
    return this.log.signatureOutHirer || this.log.signatureOut || null;
  }

  getCustomerNameOut(): string | null {
    // Check new field first, then fallback to legacy field
    return this.log.signatureOutHirerName || this.log.signatureOutName || null;
  }

  getRepresentativeSignatureOut(): string | null {
    // New field only (no legacy equivalent for representative)
    return this.log.signatureOutCarRental || null;
  }

  getRepresentativeNameOut(): string | null {
    // New field only (no legacy equivalent for representative)
    return this.log.signatureOutCarRentalName || null;
  }

  getCustomerSignatureIn(): string | null {
    // Check new field first, then fallback to legacy field
    return this.log.signatureInHirer || this.log.signatureIn || null;
  }

  getCustomerNameIn(): string | null {
    // Check new field first, then fallback to legacy field
    return this.log.signatureInHirerName || this.log.signatureInName || null;
  }

  getRepresentativeSignatureIn(): string | null {
    // New field only (no legacy equivalent for representative)
    return this.log.signatureInCarRental || null;
  }

  getRepresentativeNameIn(): string | null {
    // New field only (no legacy equivalent for representative)
    return this.log.signatureInCarRentalName || null;
  }

  get items(): FormArray {
    return this.returnForm?.get("items") as FormArray;
  }

  patchCheckoutItems(): void {
    if (this.returnForm && this.log && this.log.checkoutItems) {
      const itemsArray = this.returnForm.get("items") as FormArray;

      // Clear existing items in the FormArray
      itemsArray.clear();

      // Split the `checkoutItems` string from the backend
      const checkoutItemsArray = this.log.checkoutItems.split(",").map((item) => item.trim());

      // Add each item to the FormArray
      checkoutItemsArray.forEach((item) => {
        itemsArray.push(
          this.fb.group({
            name: [item], // Item name from the backend
            systemChecked: [true], // Pre-checked (disabled)
          }),
        );
      });

      console.log("FormArray after patching:", itemsArray.value);
    } else {
      console.warn("No checkout items available or form is not initialized.");
    }
  }

  removeDamage(index: number): void {
    if (this.log.damageInfoIn) {
      this.log.damageInfoIn.splice(index, 1); // Remove the item at the specified index
    }
  }

  async downloadPDF() {
    this.isGeneratingPDF.set(true);
    try {
      await this.pdfService.generatePDF("complete-form", "complete-form");
      this.toast.success("PDF generated successfully. Check your downloads");
    } catch (error) {
      console.log("Error downloading the PDF");
      console.log(error);
      this.toast.error("Failed to generate PDF");
    } finally {
      this.isGeneratingPDF.set(false);
    }
  }

  get sortedDamageInfoOut(): any[] {
    if (!this.log?.damageInfoOut) return [];

    return [...this.log.damageInfoOut].sort((a, b) => {
      const areaA = parseInt(a.area, 10);
      const areaB = parseInt(b.area, 10);
      return areaA - areaB;
    });
  }

  get sortedDamageInfoIn(): any[] {
    if (!this.log?.damageInfoIn) return [];

    return [...this.log.damageInfoIn].sort((a, b) => {
      const areaA = parseInt(a.area, 10);
      const areaB = parseInt(b.area, 10);
      return areaA - areaB;
    });
  }

  getTotalMileageAllowance(): number {
    if (this.log.vehicle.maxDailyMileage === 0) {
      return 0;
    }

    const baseMileage = this.log.vehicle.maxDailyMileage;
    const extraMileage =
      this.log.promotion?.promotionType === "EXTRA_MILEAGE" ? this.log.promotion.extraMileage : 0;

    return baseMileage + extraMileage;
  }

  calculateDaysHired(): number {
    if (this.log.invoices && this.log.invoices.length > 0) {
      const rentalItems = this.log.invoices[0].invoiceItemResult.filter((item) =>
        item.description.includes("Car rental for"),
      );
      if (rentalItems.length > 0) {
        return rentalItems.length;
      }
    }

    // Default fallback
    return 1;
  }

  getReturnMileageLimit() {
    const daysHired = this.calculateDaysHired();
    const totalMileageAllowance = this.getTotalMileageAllowance();

    // Determine starting mileage based on booking state
    let startingMileage: number;
    if (this.log.status === "BOOKED") {
      startingMileage = this.log.vehicle.mileage;
    } else {
      startingMileage = this.log.mileageOut ?? 0;
    }

    const totalAllowedMileage = daysHired * totalMileageAllowance;

    return totalAllowedMileage === 0 ? 0 : startingMileage + totalAllowedMileage;
  }
}
