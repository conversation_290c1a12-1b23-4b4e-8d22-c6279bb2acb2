<table class="table table-striped mb-0 styled-table text-center">
  <thead class="">
    <tr class="text-center">
      <th (click)="sort('id')">
        Id
        <app-sort-icon [sortBy]="sortBy" column="id" [directionAsc]="directionAsc"></app-sort-icon>
      </th>
      <th (click)="sort('start')">
        Start Date
        <app-sort-icon
          [sortBy]="sortBy"
          column="start"
          [directionAsc]="directionAsc"
        ></app-sort-icon>
      </th>
      <th (click)="sort('vehicle.model')">
        Model
        <app-sort-icon
          [sortBy]="sortBy"
          column="vehicle.model"
          [directionAsc]="directionAsc"
        ></app-sort-icon>
      </th>
      <th (click)="sort('vehicle.regno')">
        Reg Number
        <app-sort-icon
          [sortBy]="sortBy"
          column="vehicle.regno"
          [directionAsc]="directionAsc"
        ></app-sort-icon>
      </th>
      <!-- <th *ngIf="userType == 'admin'"  >
        Payment Type
      </th> -->
      <th *ngIf="userType == 'admin'" (click)="sort('vehicle.agency.name')">
        Provider
        <app-sort-icon
          [sortBy]="sortBy"
          column="vehicle.agency.name"
          [directionAsc]="directionAsc"
        ></app-sort-icon>
      </th>
      <th>Days Hired</th>
      <th>Amount {{ getHeaderCurrency() }}</th>
      <th *ngIf="userType == 'admin'">Reservation<br />Fee {{ getHeaderCurrency() }}</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let booking of page?.content">
      <td>
        {{ booking?.id }}
        <small *ngIf="userType == 'admin'">
          <span *ngIf="booking?.byAgency" class="badge badge-info ml-1" title="Created by Agency">
            <i class="fas fa-building"></i>
          </span>
          <span
            *ngIf="!booking?.byAgency"
            class="badge badge-warning ml-1"
            title="Created by Hirer"
          >
            <i class="fas fa-user-shield"></i>
          </span>

          <span
            *ngIf="booking?.invoices?.[0]?.payments?.[0]?.paymentType as paymentType"
            class="badge badge-main ml-1"
          >
            <ng-container [ngSwitch]="paymentType">
              <i
                *ngSwitchCase="'STRIPE'"
                class="fab fa-cc-stripe text-primary"
                title="Stripe Payment"
              ></i>
              <i
                *ngSwitchCase="'PAYNOW'"
                class="fas fa-mobile-alt text-success"
                title="PayNow Payment"
              ></i>
              <i
                *ngSwitchCase="'CASH'"
                class="fas fa-money-bill-wave text-warning"
                title="Cash Payment"
              ></i>
              <i
                *ngSwitchDefault
                class="fas fa-question-circle text-muted"
                title="Unknown Payment Type"
              ></i>
            </ng-container>
          </span>
        </small>
      </td>
      <td>{{ booking?.start ? (booking?.start | date: "dd/MM/yyyy") : "N/A" }}</td>
      <td>{{ booking?.vehicle.name + " " + booking?.vehicle.model }}</td>
      <td>{{ booking?.vehicle.regno }}</td>
      <!-- <td *ngIf="userType == 'admin'" >{{ booking?.invoices[0]?.payments[0]?.paymentType }}</td> -->
      <td *ngIf="userType == 'admin'">{{ booking?.vehicle.agency.name }}</td>
      <td>{{ daysHired(booking) }}</td>
      <td>
        {{ booking?.invoices[0]?.totalAmount | contextualCurrency: booking }}
      </td>
      <td *ngIf="userType == 'admin'">
        {{
          !booking?.byAgency
            ? (booking?.invoices[0]?.subTotalAmount * 0.07 | contextualCurrency: booking)
            : "_"
        }}
      </td>
      <td>
        <div (click)="openActionsModal(booking, actionsModal)">
          <i class="fas fa-ellipsis-h mr-1"></i>
          <span
            *ngIf="booking.promotion != null"
            class="badge badge-success ml-1"
            title="Booking has promotion"
          >
            <i class="fas fa-tag"></i>
          </span>
        </div>
      </td>
    </tr>
  </tbody>
</table>

<section class="pagination-container" *ngIf="page">
  <div class="container-fluid">
    <div class="row m-0">
      <div class="col-sm-6 d-flex col-md-6 text-left">
        <span class="acontent-center pr-2">
          Showing {{ page.number * page.size + 1 }} -
          {{ page.number * page.size + page.numberOfElements }} of
          {{ page.totalElements }}
        </span>

        <select
          style="width: min-content; justify-self: right"
          class="form-control"
          [(ngModel)]="pageSize"
          (change)="resetPageNumber(); refreshResource()"
        >
          <option *ngFor="let size of [10, 25, 50, 100]" [value]="size">{{ size }}</option>
        </select>
      </div>
      <div class="col-sm-6 col-md-6 text-right">
        <button class="btn text-white" (click)="handlePageChange('prev')" [disabled]="page.first">
          <i class="fa fa-caret-up"></i>&nbsp;&nbsp;Previous Page
        </button>
        <button class="btn text-white" (click)="handlePageChange('next')" [disabled]="page.last">
          Next Page&nbsp;&nbsp;<i class="fa fa-caret-down"></i>
        </button>
      </div>
    </div>
  </div>
</section>

<!-- Modals -->

<!-- Job Info Modal -->
<ng-template #vehicleHandOver>
  <div class="card">
    <handover-modal
      [log]="selectedVehicle"
      (updated)="refreshPage($event)"
      [refreshBookings]="refreshBookings"
    >
    </handover-modal>
  </div>
</ng-template>

<ng-template #invoicesModal let-modal>
  <div style="width: 600px">
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center w-100">Booking Invoices</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <div class="table-responsive">
        <table class="table styled-table">
          <thead>
            <tr>
              <th>Invoice ID</th>
              <th>Date</th>
              <th>Amount {{ getHeaderCurrency(true, selectedVehicle) }}</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            @for (invoice of selectedVehicle?.invoices; track invoice.id) {
              <tr>
                <td>{{ invoice.id }}</td>
                <td>{{ invoice.invoiceDate | date: "dd/MM/yyyy" }}</td>
                <td>{{ invoice.totalAmount | contextualCurrency: selectedVehicle }}</td>
                <td>
                  <button class="btn btn-link" (click)="debitNote(invoice.id); modal.dismiss()">
                    <i class="fa fa-eye" title="View Invoice Details"></i>
                  </button>
                </td>
              </tr>
            } @empty {
              <tr>
                <td colspan="4">No Invoices</td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #vehicleReturn>
  <div class="card">
    <return-modal [log]="selectedVehicle" (updated)="refreshPage($event)"> </return-modal>
  </div>
</ng-template>

<!-- Edit Job Modal -->
<ng-template #vehicleComplete>
  <div class="card">
    <complete-modal [log]="selectedVehicle" (updated)="refreshPage($event)"> </complete-modal>
  </div>
</ng-template>

<ng-template #jobInfo>
  <div class="card">
    <transport-info-modal [log]="selectedVehicle" (updated)="refreshPage($event)">
    </transport-info-modal>
  </div>
</ng-template>

<!-- Delete Job Modal -->
<ng-template #deleteModal>
  <div class="card">
    <app-delete-transport-modal [selectedJob]="selectedJob" (updated)="refreshPage($event)">
    </app-delete-transport-modal>
  </div>
</ng-template>

<!-- Report Modal -->
<ng-template #reportForm>
  <app-report-form></app-report-form>
</ng-template>

<!-- Resend Email Modal -->
<ng-template #resendEmailModal let-modal>
  <div>
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center w-100">Resend Booking Email</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss(); cancelResendEmail()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <div class="container">
        <!-- Email Type Selection View -->
        <div *ngIf="!isConfirmingEmail">
          <div class="row mb-3">
            <div class="col-12">
              <h6>Booking ID: {{ selectedVehicle?.id }}</h6>
              <p class="text-muted">Select the type of email you want to resend:</p>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <div class="list-group">
                <button
                  *ngFor="let type of emailTypes"
                  class="list-group-item list-group-item-action"
                  (click)="prepareResendEmail(selectedVehicle, type.value)"
                  [ngStyle]="{ display: shouldShowEmailType(type.value) ? 'block' : 'none' }"
                >
                  {{ type.label }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Confirmation View -->
        <div *ngIf="isConfirmingEmail">
          <div class="row mb-3">
            <div class="col-12 text-center">
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <strong>Confirmation Required</strong>
              </div>
              <p>Are you sure you want to resend the following email?</p>
              <h5 class="mt-3 mb-3">{{ selectedEmailTypeDisplay }}</h5>
            </div>
          </div>

          <!-- Recipient Information -->
          <div class="row mb-3">
            <div class="col-12">
              <div class="card">
                <div class="card-header bg-light">
                  <strong>Recipient Information</strong>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <p class="mb-1">
                        <strong>Name:</strong> {{ selectedVehicle?.firstname }}
                        {{ selectedVehicle?.surname }}
                      </p>
                      <p class="mb-1"><strong>Email:</strong> {{ selectedVehicle?.email }}</p>
                      <p class="mb-0"><strong>Phone:</strong> {{ selectedVehicle?.phone }}</p>
                    </div>
                    <div class="col-md-6">
                      <p class="mb-1"><strong>Booking ID:</strong> {{ selectedVehicle?.id }}</p>
                      <p class="mb-1">
                        <strong>Vehicle:</strong> {{ selectedVehicle?.vehicle?.name }}
                        {{ selectedVehicle?.vehicle?.model }}
                      </p>
                      <p class="mb-0">
                        <strong>Reg Number:</strong> {{ selectedVehicle?.vehicle?.regno }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <p class="text-muted mt-3 text-center">
                This will send a new email to the recipient shown above.
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- </div>
    <div class="modal-footer"> -->
      <!-- Different footer buttons based on state -->
      <div *ngIf="!isConfirmingEmail" class="d-flex w-100 mt-1 justify-content-between">
        <button
          type="button"
          class="btn btn-secondary"
          (click)="modal.dismiss(); cancelResendEmail()"
        >
          Cancel
        </button>
      </div>
      <div *ngIf="isConfirmingEmail" class="d-flex w-100 mt-1 justify-content-between">
        <button type="button" class="btn btn-secondary" (click)="cancelResendEmail()">Back</button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="confirmResendEmail(selectedVehicle); modal.dismiss()"
        >
          Confirm Send
        </button>
      </div>
    </div>
  </div>
</ng-template>

<!-- Actions Modal -->
<ng-template #actionsModal let-modal>
  <div>
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center w-100">Booking Actions</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <div class="container">
        <!-- Booking Information -->
        <div class="row mb-3">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-light">
                <strong>Booking Information</strong>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <p class="mb-1"><strong>Booking ID:</strong> {{ selectedVehicle?.id }}</p>
                    <p class="mb-1">
                      <strong>Customer:</strong> {{ selectedVehicle?.firstname }}
                      {{ selectedVehicle?.surname }}
                    </p>
                    <p class="mb-0"><strong>Email:</strong> {{ selectedVehicle?.email }}</p>
                    <p class="mb-0"><strong>Phone Number:</strong> {{ selectedVehicle?.phone }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1">
                      <strong>Vehicle:</strong> {{ selectedVehicle?.vehicle?.name }}
                      {{ selectedVehicle?.vehicle?.model }}
                    </p>
                    <p class="mb-1">
                      <strong>Reg Number:</strong> {{ selectedVehicle?.vehicle?.regno }}
                    </p>
                    <p class="mb-0">
                      <strong>Start Date:</strong> {{ selectedVehicle?.start | date: "dd/MM/yyyy" }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Available Actions -->
        <div class="row">
          <div class="col-12">
            <!-- Cancellation Reason (only shown for cancelled bookings) -->
            <div
              *ngIf="selectedVehicle?.status === 'CANCELLED' && selectedVehicle?.cancelReason"
              class="alert alert-info mb-3"
            >
              <h6 class="font-weight-bold mb-2">Cancellation Reason:</h6>
              <p class="mb-0">{{ selectedVehicle.cancelReason }}</p>
            </div>

            <h6 class="mb-3">Available Actions:</h6>
            <div class="list-group">
              <!-- Handover Action -->
              <button
                *ngIf="selectedVehicle?.status === 'BOOKED'"
                class="list-group-item list-group-item-action d-flex align-items-center"
                (click)="viewDetails(selectedVehicle, vehicleHandOver); modal.dismiss()"
              >
                <i class="fa fa-car mr-3 text-primary-custom"></i>
                <div>
                  <strong>Vehicle Handover</strong>
                  <p class="mb-0 small text-muted">Process vehicle handover to customer</p>
                </div>
              </button>

              <!-- Return Action -->
              <button
                *ngIf="selectedVehicle?.status === 'WAITINGAUTH' && userType == 'agency'"
                class="list-group-item list-group-item-action d-flex align-items-center"
                (click)="viewDetails(selectedVehicle, vehicleReturn); modal.dismiss()"
              >
                <i class="fa fa-undo mr-3 text-primary-custom"></i>
                <div>
                  <strong>Vehicle Return</strong>
                  <p class="mb-0 small text-muted">Process vehicle return from customer</p>
                </div>
              </button>

              <!-- Complete Action -->
              <button
                *ngIf="selectedVehicle?.status === 'COMPLETE'"
                class="list-group-item list-group-item-action d-flex align-items-center"
                (click)="viewDetails(selectedVehicle, vehicleComplete); modal.dismiss()"
              >
                <i class="fa fa-check-circle mr-3 text-success"></i>
                <div>
                  <strong>View Completed Booking</strong>
                  <p class="mb-0 small text-muted">View details of completed booking</p>
                </div>
              </button>

              <!-- View Invoices Action -->
              <button
                class="list-group-item list-group-item-action d-flex align-items-center"
                (click)="viewDetails(selectedVehicle, invoicesModal); modal.dismiss()"
              >
                <i class="fa fa-file mr-3 text-primary-custom"></i>
                <div>
                  <strong>View Invoices</strong>
                  <p class="mb-0 small text-muted">View and manage booking invoices</p>
                </div>
              </button>

              <!-- Resend Email Action -->
              <button
                class="list-group-item list-group-item-action d-flex align-items-center"
                (click)="openResendEmailModal(selectedVehicle, resendEmailModal); modal.dismiss()"
              >
                <i class="fas fa-envelope mr-3 text-primary-custom"></i>
                <div>
                  <strong>Resend Email</strong>
                  <p class="mb-0 small text-muted">
                    Resend booking confirmation or notification emails
                  </p>
                </div>
              </button>

              <!-- Cancel Booking Action -->
              <button
                *ngIf="
                  selectedVehicle?.status !== 'CANCELLED' && selectedVehicle?.status !== 'COMPLETE'
                "
                class="list-group-item list-group-item-action d-flex align-items-center"
                (click)="
                  openCancelBookingModal(selectedVehicle, cancelBookingModal); modal.dismiss()
                "
              >
                <i class="fas fa-times-circle mr-3 text-danger"></i>
                <div>
                  <strong>Cancel Booking</strong>
                  <p class="mb-0 small text-muted">Cancel this vehicle booking</p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<!-- Cancel Booking Modal -->
<ng-template #cancelBookingModal let-modal>
  <div>
    <div class="modal-header bg-danger text-white">
      <h5 class="modal-title text-center w-100">Cancel Booking</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss(); cancelCancellation()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <div class="container">
        <div class="row mb-3">
          <div class="col-12 text-center">
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              <strong>Warning: This action cannot be undone</strong>
            </div>
            <p>Are you sure you want to cancel this booking?</p>
          </div>
        </div>

        <!-- Booking Information -->
        <div class="row mb-3">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-light">
                <strong>Booking Information</strong>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <p class="mb-1"><strong>Booking ID:</strong> {{ selectedVehicle?.id }}</p>
                    <p class="mb-1">
                      <strong>Customer:</strong> {{ selectedVehicle?.firstname }}
                      {{ selectedVehicle?.surname }}
                    </p>
                    <p class="mb-0"><strong>Email:</strong> {{ selectedVehicle?.email }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1">
                      <strong>Vehicle:</strong> {{ selectedVehicle?.vehicle?.name }}
                      {{ selectedVehicle?.vehicle?.model }}
                    </p>
                    <p class="mb-1">
                      <strong>Reg Number:</strong> {{ selectedVehicle?.vehicle?.regno }}
                    </p>
                    <p class="mb-0">
                      <strong>Start Date:</strong> {{ selectedVehicle?.start | date: "dd/MM/yyyy" }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Cancellation Reason Form -->
        <div class="row" *ngIf="isReasonRequired">
          <div class="col-12">
            <div class="form-group">
              <label for="cancelReason" class="font-weight-bold">
                Cancellation Reason <span class="text-danger">*</span>
              </label>
              <textarea
                id="cancelReason"
                class="form-control"
                rows="3"
                [(ngModel)]="cancelReason"
                placeholder="Please provide a reason for cancellation"
                [ngClass]="{ 'is-invalid': isReasonInvalid }"
              ></textarea>
              <div class="invalid-feedback" *ngIf="isReasonInvalid">
                A reason is required for cancellations within 48 hours of the booking start time.
              </div>
              <small class="text-muted">
                This booking is within 48 hours of the start time, so a cancellation reason is
                required.
              </small>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer buttons -->
      <div class="d-flex w-100 mt-3 justify-content-between">
        <button
          type="button"
          class="btn btn-secondary"
          (click)="modal.dismiss(); cancelCancellation()"
        >
          Back
        </button>
        <button
          type="button"
          class="btn btn-danger"
          (click)="confirmCancelBooking(selectedVehicle); modal.dismiss()"
        >
          Confirm Cancellation
        </button>
      </div>
    </div>
  </div>
</ng-template>
