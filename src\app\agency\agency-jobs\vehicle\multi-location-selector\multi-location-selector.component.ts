import {
    Component,
    OnInit,
    OnChanges,
    SimpleChanges,
    Input,
    forwardRef,
    ElementRef,
    HostListener,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

export interface Location {
    id: number;
    city: string;
    cityAscii: string | null;
    lng: number | null;
    lat: number | null;
    country: string;
    name: string;
    iso3: string | null;
    iso2: string | null;
    capital: string | null;
    adminName: string | null;
}

@Component({
    selector: 'app-multi-location-selector',
    standalone: false,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => MultiLocationSelectorComponent),
            multi: true,
        },
    ],
    template: `
    <div class="position-relative">
      <div class="form-control multi-select-container"
           [class.disabled]="disabled"
           (click)="toggleSearch()"
           style="height: auto"
           >
        <div class="selected-locations" *ngIf="selectedLocations.length > 0">
          <span class="location-tag" *ngFor="let location of selectedLocations">
            {{ formatLocation(location) }}
            <button type="button" class="btn-close-tag"
                    (click)="removeLocation(location, $event)"
                    [disabled]="disabled">×</button>
          </span>
        </div>
        <span class="placeholder-text" *ngIf="selectedLocations.length === 0">
          {{ placeholder }}
        </span>
      </div>

      <div
        *ngIf="isSearching"
        class="search-container position-absolute w-100 bg-white border rounded shadow-sm"
      >
        <div class="p-2">
          <input
            type="text"
            class="form-control"
            [(ngModel)]="searchText"
            (input)="onSearch()"
            [placeholder]="searchPlaceholder"
            #searchInput
          />
        </div>
        <div class="dropdown-menu d-block w-100 position-relative border-0">
          <a
            class="dropdown-item"
            *ngFor="let location of filteredLocations"
            (click)="toggleLocation(location)"
            [class.selected]="isLocationSelected(location)"
          >
            <input type="checkbox"
                   [checked]="isLocationSelected(location)"
                   class="me-2" readonly>
            {{ formatLocation(location) }}
          </a>
        </div>
      </div>
    </div>
  `,
    styles: [
        `
      .multi-select-container {
        min-height: 38px;
        cursor: pointer;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 4px;
        padding: 6px 12px;
      }

      .multi-select-container.disabled {
        background-color: #e9ecef;
        cursor: not-allowed;
      }

      .selected-locations {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        width: 100%;
      }

      .location-tag {
        background-color: var(--primarycustom);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .btn-close-tag {
        background: none;
        border: none;
        color: white;
        font-size: 14px;
        cursor: pointer;
        padding: 0;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
      }

      .btn-close-tag:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .btn-close-tag:disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }

      .placeholder-text {
        color: #6c757d;
        font-style: italic;
      }

      .dropdown-menu {
        max-height: 200px;
        overflow-y: auto;
        width: 100% !important;
        left: 0 !important;
        right: 0 !important;
        margin-top: 0;
      }

      .dropdown-item {
        cursor: pointer;
        display: flex;
        align-items: center;
      }

      .dropdown-item:hover {
        background-color: #e9ecef;
      }

      .dropdown-item.selected {
        background-color: #007bff;
        color: white;
      }

      .search-container {
        z-index: 1000;
      }
    `,
    ],
})
export class MultiLocationSelectorComponent implements OnInit, OnChanges, ControlValueAccessor {
    @Input() locations: Location[] = [];
    @Input() id: string =
        'multi-location-combobox-' + Math.random().toString(36).substring(2);
    @Input() searchPlaceholder: string = 'Search locations...';
    @Input() placeholder: string = 'Select locations...';
    @Input() disabled = false;

    filteredLocations: Location[] = [];
    searchText: string = '';
    selectedLocations: Location[] = [];
    selectedLocationIds: number[] = [];
    isSearching: boolean = false;

    private onChange: (value: number[]) => void = () => { };
    private onTouched: () => void = () => { };

    constructor(private elementRef: ElementRef) { }

    ngOnInit() {
        this.filteredLocations = this.locations;
        console.log('MultiLocationSelector - ngOnInit - locations:', this.locations);
        console.log('MultiLocationSelector - ngOnInit - selectedLocationIds:', this.selectedLocationIds);
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['locations'] && changes['locations'].currentValue) {
            console.log('MultiLocationSelector - ngOnChanges - locations changed:', changes['locations'].currentValue);
            this.filteredLocations = this.locations;
            // Re-filter selected locations when locations array changes
            this.updateSelectedLocations();
        }
    }

    private updateSelectedLocations() {
        if (this.selectedLocationIds && this.selectedLocationIds.length > 0) {
            this.selectedLocations = this.locations.filter(loc =>
                this.selectedLocationIds.includes(loc.id)
            );
            console.log('MultiLocationSelector - updateSelectedLocations - updated selectedLocations:', this.selectedLocations);
        }
    }

    @HostListener('document:click', ['$event'])
    onClickOutside(event: Event) {
        if (!this.elementRef.nativeElement.contains(event.target)) {
            this.isSearching = false;
        }
    }

    formatLocation(location: Location): string {
        return `${location.name ? (location.name + ', ') : ''}${location.city}, ${location.country}`;
    }

    onSearch() {
        const searchTerm = this.searchText.toLowerCase();
        this.filteredLocations = this.locations.filter(
            (location) =>
                location.city.toLowerCase().includes(searchTerm) ||
                location.country.toLowerCase().includes(searchTerm) ||
                location.name?.toLowerCase().includes(searchTerm)
        );
    }

    toggleLocation(location: Location) {
        if (this.isLocationSelected(location)) {
            this.removeLocationById(location.id);
        } else {
            this.addLocation(location);
        }
    }

    addLocation(location: Location) {
        if (!this.isLocationSelected(location)) {
            this.selectedLocations.push(location);
            this.selectedLocationIds.push(location.id);
            this.onChange(this.selectedLocationIds);
            this.onTouched();
        }
    }

    removeLocation(location: Location, event?: Event) {
        if (event) {
            event.stopPropagation();
        }
        this.removeLocationById(location.id);
    }

    removeLocationById(locationId: number) {
        this.selectedLocations = this.selectedLocations.filter(loc => loc.id !== locationId);
        this.selectedLocationIds = this.selectedLocationIds.filter(id => id !== locationId);
        this.onChange(this.selectedLocationIds);
        this.onTouched();
    }

    isLocationSelected(location: Location): boolean {
        return this.selectedLocationIds.includes(location.id);
    }

    toggleSearch() {
        if (this.disabled) return;

        this.isSearching = !this.isSearching;
        if (this.isSearching) {
            this.filteredLocations = this.locations;
            setTimeout(() => {
                document
                    .querySelector<HTMLInputElement>(
                        `input[placeholder="${this.searchPlaceholder}"]`
                    )
                    ?.focus();
            });
        }
    }

    writeValue(locationIds: number[] | null): void {
        console.log('MultiLocationSelector - writeValue called with:', locationIds);
        console.log('MultiLocationSelector - available locations:', this.locations);
        this.selectedLocationIds = locationIds || [];
        this.updateSelectedLocations();
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState?(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }
}
