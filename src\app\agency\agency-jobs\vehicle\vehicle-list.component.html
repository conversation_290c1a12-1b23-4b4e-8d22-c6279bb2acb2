<main class="mt-5">
  <!-- Header part -->
  <div class="container-fluid">
    <div class="row items-center">
      <div class="col-12 col-sm-12 col-md-4 text-left">
        <h3 class="mb-0">Fleet Management</h3>
      </div>

      <div class="col-12 col-sm-6 col-md-4 pr-0 justify-right"></div>
    </div>
  </div>

  <!-- Content -->
  <section class="content mt-4">
    <div class="container-fluid">
      <div class="row mb-3">
        <div class="col-md-2">
          <input
            type="text"
            class="form-control"
            placeholder="Search"
            [ngModel]="searchMake()"
            (ngModelChange)="onSearchMakeChange($event)"
            (keyup.enter)="applyFilter()"
          />
        </div>
        <div class="col-md-3">
          <select
            class="form-control"
            [ngModel]="searchStatus()"
            (ngModelChange)="searchStatus.set($event); resetPageNumber()"
          >
            <option value="">All Status</option>
            <option value="AVAILABLE">Available</option>
            <option value="AWAITING">Awaiting</option>
            <option value="EDITED">Edited</option>
          </select>
        </div>
        <!-- <div class="col-md-2">
          <input
            type="text"
            class="form-control"
            placeholder="Model"
            [ngModel]="searchModel()"
            (ngModelChange)="searchModel.set($event)"
          />
        </div> -->
        <!-- <div class="col-md-2">
          <input
            type="text"
            class="form-control"
            placeholder="Location"
            [ngModel]="searchLocation()"
            (ngModelChange)="searchLocation.set($event)"
          />
        </div> -->

        <div class="col-md-3" *ngIf="userType == 'admin'">
          <input
            type="text"
            class="form-control"
            placeholder="Provider"
            [ngModel]="searchProvider()"
            (ngModelChange)="searchProvider.set($event)"
          />
        </div>

        <div class="col-md-4">
          <button class="btn btn-primary" (click)="applyFilter()">
            <i class="fa fa-search"></i> Search
          </button>
          <button class="btn btn-secondary ml-2" (click)="clearFilters()">
            <i class="fa fa-times"></i> Clear
          </button>
        </div>

        <span *ngIf="!isCarRental" class="pl-2">
          <button class="btn btn-success" (click)="formInit(add)">
            Add Vehicle
          </button>
        </span>
        <span *ngIf="isCarRental && !('VIEW_AGENCY' | permission)" class="pl-2">
          <button
            class="btn btn-success"
            (click)="formInitRental(addRentalVehicleModal)"
          >
            Add Vehicle
          </button>
        </span>
        <span *ngIf="isCarRental && !('VIEW_AGENCY' | permission)" class="pl-2">
          <button
            class="btn btn-success"
            (click)="formInitRental(vehiclesLink)"
          >
            Share Link
          </button>
        </span>
      </div>
      <table class="table table-striped mb-0 styled-table text-left">
        <thead>
          <tr class="text-left">
            <th
              *ngIf="userType == 'admin'"
              (click)="sort('agency.name')"
              class="pointer"
            >
              Provider
              <app-sort-icon
                [sortBy]="sortBy()"
                column="agency.name"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th (click)="sort('name')" class="pointer">
              Make
              <app-sort-icon
                [sortBy]="sortBy()"
                column="name"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th (click)="sort('model')" class="pointer">
              Model
              <app-sort-icon
                [sortBy]="sortBy()"
                column="model"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th *ngIf="isRental" (click)="sort('fuelType')" class="pointer">
              Fuel Type
              <app-sort-icon
                [sortBy]="sortBy()"
                column="fuelType"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th (click)="sort('regno')" class="pointer">
              Reg No
              <app-sort-icon
                [sortBy]="sortBy()"
                column="regno"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th>Locations</th>
            <th (click)="sort('status')" class="pointer">
              Status
              <app-sort-icon
                [sortBy]="sortBy()"
                column="status"
                [directionAsc]="directionAsc()"
              ></app-sort-icon>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            *ngFor="let d of filteredVehicles()"
            class="pointer"
            [routerLink]="['/', userType, 'jobs', 'vehicle', 'view', d.id]"
            style="cursor: pointer"
          >
            <td *ngIf="userType == 'admin'">
              {{ d.agency.name }}
            </td>
            <td>{{ d.name }}</td>
            <td>{{ d.model }}</td>
            <td *ngIf="isRental">{{ d.fuelType }}</td>
            <td>{{ d.regno }}</td>
            <td class="locations-column">
              <span
                *ngIf="d.locations && d.locations.length > 0; else noLocations"
                class="locations-content"
              >
                <span class="badge badge-primary badge-pill location-badge">{{
                  d.locations.length
                }}</span>
                <span>
                  <span *ngFor="let location of d.locations; let last = last">
                    {{ location.city }}<span *ngIf="!last">, </span>
                  </span>
                </span>
              </span>
              <ng-template #noLocations>
                <span class="text-muted">No locations</span>
              </ng-template>
            </td>
            <td>{{ d.status }}</td>
            <td>
              <!-- <a [routerLink]="['/', userType, 'jobs', 'vehicle', 'view', d.id]">
                <span class="mr-3 text-primary-custom pointer">
                  <i class="fa fa-eye" title="edit"></i>
                </span>
              </a> -->

              <a
                [routerLink]="['/', userType, 'jobs', 'vehicle', 'view', d.id]"
                [queryParams]="{ returnToList: 'true' }"
                ><span class="mr-3 text-primary-custom pointer">
                  <i class="fa fa-eye" title="view"></i> </span
              ></a>

              <!-- Availability calendar button -->
              <span
                class="mr-3 text-info pointer"
                (click)="openAvailabilityModal(d, $event)"
              >
                <i class="fas fa-calendar-alt" title="Manage availability"></i>
              </span>

              <!-- Promotion indicator and button -->
              <span
                *ngIf="hasPromotions(d)"
                class="mr-3 text-success pointer"
                (click)="openPromotionsModal(d, promotionsModal)"
              >
                <!-- <small class="ml-1 badge badge-success">{{ d.promotions?.length }}</small> -->
                <i
                  class="fas fa-tag"
                  title="Has promotions"
                  style="font-size: 1.1em"
                ></i>
              </span>

              <app-warning-icon [job]="d"></app-warning-icon>

              <!-- <span class="text-danger-custom pointer" (click)="loadDirectorate(d.id, delete, false)">
                    <i class="fa fa-trash-alt" title="delete"></i>
                </span> -->
            </td>
          </tr>
        </tbody>
      </table>

      <section class="pagination-container" *ngIf="vehicles()">
        <div class="container-fluid">
          <div class="row m-0">
            <div
              class="col-12 col-sm-12 col-md-5 text-left acontent-center d-flex"
            >
              <span class="acontent-center pr-2">
                Showing {{ pageNumber() * pageSize() + 1 }} - {{ showin() }} of
                {{ totalItems() }}
              </span>

              <select
                style="width: min-content; justify-self: right"
                class="form-control"
                [ngModel]="pageSize()"
                (ngModelChange)="
                  pageSize.set($event); resetPageNumber(); getVehicles()
                "
              >
                <option
                  *ngFor="let size of [10, 20, 25, 50, 100]"
                  [value]="size"
                >
                  {{ size }}
                </option>
              </select>
            </div>
            <div class="col-12 col-sm-6 col-md-4 text-right">
              <button
                class="btn text-white"
                (click)="handlePageChange('prev')"
                [disabled]="first()"
              >
                <i class="fa fa-caret-up"></i>&nbsp;&nbsp;Previous Page
              </button>
            </div>
            <div class="col-12 col-sm-6 col-md-3 text-right pr-0">
              <button
                class="btn text-white"
                (click)="handlePageChange('next')"
                [disabled]="last()"
              >
                Next Page&nbsp;&nbsp;<i class="fa fa-caret-down"></i>
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </section>
</main>

<ng-template #inventoryWarningModal let-modal>
  <div class="modal-header bg-custom-danger text-white">
    <h5 class="modal-title">Item Expiry Warning</h5>
    <!-- <button
      type="button"
      class="close text-white"
      aria-label="Close"
      (click)="modal.dismiss()"
    >
      <span aria-hidden="true">&times;</span>
    </button> -->
  </div>
  <div class="modal-body">
    <ng-container *ngIf="selectedInventory() as inventory">
      <p>
        <strong>Date Installed:</strong>
        {{ inventory?.dateInstalled | date : "yyyy-MM-dd" }}
      </p>
      <p>
        <strong>Next Check Date:</strong>
        {{ inventory?.nextCheckDate | date : "yyyy-MM-dd" }}
      </p>
      <p class="text-danger">This Item item is expiring soon!</p>
      <div class="modal-footer bg-white">
        <button type="button" class="btn btn-success" (click)="modal.dismiss()">
          Close
        </button>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template class="modal fade" #add let-modal>
  <div class="">
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center mx-4 w-100">Add Vehicle</h5>
      <span
        type="button"
        class="btn close text-white"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <form [formGroup]="addForm" (ngSubmit)="createVehicle(addForm)">
        <div class="bg-modal card-body">
          <div class="container-fluid">
            <div class="">
              <div class="">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">Vehicle Details</span>
                <hr class="bg-black mt-0" />
              </div>
              <div class="">
                <div class="form-group">
                  <!-- <label>Name</label> -->
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="name"
                    placeholder="Body Type"
                    class="form-control"
                    [class.is-invalid]="submitted() && addForm.get('name')?.errors?.['required']"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('name')?.errors?.['required']"
                  >
                    Body Type is required
                  </div>
                </div>
              </div>
              <div class="">
                <div class="form-group">
                  <!-- <label>Descriptio</label> -->
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="model"
                    placeholder="Model"
                    class="form-control"
                    [class.is-invalid]="submitted() && addForm.get('model')?.errors?.['required']"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('model')?.errors?.['required']"
                  >
                    Model is required
                  </div>
                </div>
              </div>
              <div class="">
                <div class="form-group">
                  <!-- <label>Descriptio</label> -->
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="color"
                    placeholder="Colour"
                    class="form-control"
                  />
                </div>
              </div>
              <div class="">
                <div class="form-group">
                  <!-- <label>Descriptio</label> -->
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="regno"
                    placeholder="Registration Number"
                    class="form-control"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div align="right" class="mt-3">
          <button
            type="button"
            class="btn btn-danger btn-sm mr-3"
            (click)="modal.dismiss()"
          >
            Cancel
          </button>
          <button type="submit" class="btn btn-sm btn-success">
            Add Vehicle
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>

<ng-template class="modal fade" #addRentalVehicleModal let-modal>
  <div class="">
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center mx-4 w-100">Add Vehicle</h5>
      <span
        class="close bg-transparent text-white shadow-none"
        role="button"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <form [formGroup]="addForm" (ngSubmit)="createVehicle(addForm)">
        <div class="bg-modal card-body">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <h4>
                  <span class="ml-2">Vehicle Details</span>
                  <hr class="bg-black mt-0" />
                </h4>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <select
                    class="form-control"
                    formControlName="type"
                    placeholder=""
                  >
                    <option value="" disabled>Vehicle Type</option>
                    <option value="SUV">SUV</option>
                    <option value="PICKUP">Pick-up/Bakkie</option>
                    <option value="HATCHBACK">Hatchback</option>
                    <option value="MPV">MPV</option>
                    <option value="SEDAN">Sedan</option>
                    <option value="SPECIAL">Special/Luxury</option>
                    <option value="BUS">Bus</option>
                  </select>
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('type')?.errors?.['required']"
                  >
                    Vehicle type is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <app-vehicle-make-selector formControlName="name" />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('name')?.errors?.['required']"
                  >
                    Body Type is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="model"
                    placeholder="Vehicle Model"
                    class="form-control"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('model')?.errors?.['required']"
                  >
                    Vehicle model is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="color"
                    placeholder="Colour"
                    class="form-control"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('color')?.errors?.['required']"
                  >
                    Colour is required
                  </div>
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="number"
                    maxlength="2"
                    formControlName="doors"
                    placeholder="Number of doors"
                    class="form-control"
                    min="0"
                    max="50"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('doors')?.errors?.['required']"
                  >
                    Number of doors is required
                  </div>
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="number"
                    maxlength="2"
                    formControlName="seats"
                    placeholder="Number of Seats"
                    class="form-control"
                    min="0"
                    max="50"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('seats')?.errors?.['required']"
                  >
                    Number of seats is required
                  </div>
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="regno"
                    placeholder="Registration Number"
                    class="form-control"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('regno')?.errors?.['required']"
                  >
                    Registration number is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="engineNumber"
                    placeholder="Engine Number"
                    class="form-control"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('engineNumber')?.errors?.['required']"
                  >
                    Engine number is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="trackerId"
                    placeholder="Tracker Id"
                    class="form-control"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('trackerId')?.errors?.['required']"
                  >
                    Tracker ID is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <select
                    class="form-control"
                    formControlName="transmissionType"
                    placeholder=""
                  >
                    <option value="" disabled>Transmission Type</option>
                    <option value="AUTO">Auto</option>
                    <option value="MANUAL">Manual</option>
                  </select>
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('transmissionType')?.errors?.['required']"
                  >
                    Transmission type is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <!-- <label>Descriptio</label> -->
                  <input
                    type="text"
                    maxlength="255"
                    formControlName="engineSize"
                    placeholder="Engine Capacity"
                    class="form-control"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('engineSize')?.errors?.['required']"
                  >
                    Engine capacity is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <select
                    class="form-control"
                    formControlName="fuelType"
                    placeholder=""
                  >
                    <option value="" disabled>Fuel Type</option>
                    <option value="PETROL">Petrol</option>
                    <option value="DIESEL">Diesel</option>
                    <option value="PHYBRID">Petrol Hybrid</option>
                    <option value="DHYBRID">Diesel Hybrid</option>
                    <option value="ELECTRIC">Electric</option>
                    <option value="HYDROGEN">Hydrogen</option>
                  </select>
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('fuelType')?.errors?.['required']"
                  >
                    Fuel type is required
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <app-multi-location-selector
                    formControlName="locationIds"
                    id="locations"
                    [locations]="locations()"
                    placeholder="Select Vehicle Locations"
                  />
                  <div
                    class="error-message"
                    *ngIf="submitted() && addForm.get('locationIds')?.errors?.['required']"
                  >
                    At least one location is required
                  </div>
                </div>
              </div>

              <div class="col-md-4">
                <div class="form-group">
                  <input
                    type="checkbox"
                    maxlength="255"
                    formControlName="airConditioning"
                    placeholder="Capacity"
                    [value]="true"
                  />
                  <label> &nbsp;Air conditioning</label>
                </div>
              </div>

              <div class="col-12">
                <h5>Vehicle Daily Rate</h5>
                <div class="form-group row">
                  <div
                    *ngFor="
                      let r of ratesArray.controls;
                      let i = index;
                      trackBy: track
                    "
                    class="col-md-3"
                  >
                    <form [formGroup]="r">
                      <label>{{ r.value.desc }}</label>
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span
                            class="input-group-text"
                            style="font-size: 0.85rem"
                            >{{ baseCurrency }}</span
                          >
                        </div>
                        <input
                          type="text"
                          maxlength="255"
                          [value]="r.get('rate')?.value || ''"
                          (input)="onRateInput($event, i)"
                          placeholder="0.00"
                          class="form-control"
                          [defaultValue]="0"
                        />
                      </div>
                      <div
                        class="error-message"
                        *ngIf="submitted() && r.get('rate')?.errors?.['required']"
                      >
                        Rate for {{ r.value.desc }} is required
                      </div>
                    </form>
                  </div>
                </div>
              </div>
              <div class="form-group row col-12">
                <div class="col-md-3">
                  <h5>Deposit Required</h5>
                  <div class="form-group">
                    <div class="input-group">
                      <div class="input-group-prepend">
                        <span
                          class="input-group-text"
                          style="font-size: 0.9rem"
                          >{{ baseCurrency }}</span
                        >
                      </div>
                      <input
                        type="number"
                        maxlength="11"
                        formControlName="depositAmt"
                        placeholder="0.00"
                        class="form-control"
                        min="0"
                        oninput="validity.valid||(value='');"
                      />
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <h5>Mileage allowance per day</h5>
                  <div class="form-group">
                    <select
                      class="form-control"
                      placeholder=""
                      formControlName="maxDailyMileage"
                      value="0"
                    >
                      <option value="0" selected>Unlimited</option>
                      <option value="100">100km</option>
                      <option value="200">200km</option>
                      <option value="300">300km</option>
                      <option value="400">400km</option>
                      <option value="500">500km</option>
                    </select>
                  </div>
                </div>
                <div
                  class="col-md-3"
                  *ngIf="addForm.get('maxDailyMileage')?.value !== '0'"
                >
                  <h5>Excess mileage rate per kilometer</h5>
                  <div class="form-group">
                    <input
                      type="number"
                      class="form-control"
                      id="excessCharge"
                      formControlName="excessMileageRate"
                      min="0"
                      step="0.01"
                      placeholder="0"
                    />
                  </div>
                </div>
                <div class="col-md-3">
                  <h5>Minimum Days To Hire</h5>
                  <div class="form-group">
                    <select
                      class="form-control"
                      placeholder=""
                      formControlName="minHireDays"
                      value="1"
                    >
                      <option value="1" selected>1</option>
                      <option value="2">2</option>
                      <option value="3">3</option>
                      <option value="4">4</option>
                      <option value="5">5</option>
                      <option value="6">6</option>
                      <option value="7">7</option>
                      <option value="8">8</option>
                      <option value="9">9</option>
                      <option value="10">10</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!--
        <div class="bg-modal card-body">
          <div class="container-fluid">
            <div class="">
              <div class="">
                <i class="bi bi-info-circle"></i>
                <span class="ml-2">Vehicle Add-Ons</span><span class="subtitle">(e.g Baby Seater, Cooler Box)</span>
                <hr class="bg-black mt-0" />
              </div>

              <div class="row addon-wrapper" formArrayName="vehicleAddons">
                <div *ngFor="let addOn of vehicleAddons.controls; let i = index" [formGroupName]="i" class="col-md-3 add-on-item">

                  <div class="input-group">
                    <input type="text" class="form-control addon-input" formControlName="name" placeholder="Enter add-on" />

                    <button type="button" class="btn btn-remove" (click)="removeAddOn(i)">
                      <i class="fas fa-minus-circle remove-icon"></i>
                    </button>
                  </div>
                </div>

                 <div class="col-md-3 add-on-item" *ngIf="vehicleAddons.controls.length < maxvehicleAddons">
                  <button type="button" class="btn btn-add" (click)="addAddOn()">
                    <i class="fas fa-plus-circle add-icon"></i> Add item
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div> -->

        <div align="right" class="mt-3">
          <button
            type="button"
            class="btn btn-danger btn-sm mr-3"
            (click)="modal.dismiss()"
          >
            Cancel
          </button>
          <button type="submit" class="btn btn-sm btn-success">
            Add Vehicle
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>

<ng-template class="modal fade" #vehiclesLink let-modal>
  <div class="">
    <div class="modal-header bg-main text-white">
      <h5 class="modal-title text-center mx-4 w-100">
        Vehicles Public Listing
      </h5>
      <span
        class="close bg-transparent text-white shadow-none"
        role="button"
        data-dismiss="modal"
        aria-label="Close"
        (click)="modal.dismiss()"
      >
        <span aria-hidden="true">&times;</span>
      </span>
    </div>
    <div class="modal-body">
      <div>
        <label>Location</label>
        <select
          [ngModel]="locationId()"
          (ngModelChange)="locationId.set($event)"
          class="form-control"
        >
          <option disabled>Select Location</option>
          <option *ngFor="let l of agencyLocations" [value]="l.id">
            {{ l.city ?? l.name }}, {{ l.country }}
          </option>
        </select>
      </div>
      <div class="mt-3">
        <p *ngIf="locationId() != null">
          https://mykarlink.com/vehicles/search?location={{
            locationId()
          }}&agencyId={{ agencyId }}
        </p>
        <button
          class="btn btn-success"
          *ngIf="locationId() != null"
          (click)="copyDynamicLink(locationId(), modal)"
        >
          Copy Link
        </button>
        <p *ngIf="locationId() == null">Pick a location</p>
      </div>
    </div>
  </div>
</ng-template>

<!-- Promotions Modal -->
<ng-template #promotionsModal let-modal>
  <div class="modal-header bg-main text-white">
    <h5 class="modal-title">Vehicle Promotions</h5>
    <button
      type="button"
      class="close text-white"
      aria-label="Close"
      (click)="modal.dismiss()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <ng-container *ngIf="selectedVehiclePromotions() as promotions">
      <div *ngIf="promotions.length === 0" class="text-center">
        <p>No promotions available for this vehicle.</p>
      </div>
      <div *ngIf="promotions.length > 0">
        <ng-container *ngIf="selectedVehicle() as vehicle">
          <div class="alert alert-info">
            <strong>Vehicle:</strong> {{ vehicle?.name || "N/A" }}
            {{ vehicle?.model || "" }} ({{ vehicle?.regno || "" }})
          </div>
        </ng-container>
        <table class="table table-striped mb-0 styled-table text-left">
          <thead>
            <tr>
              <th>Title</th>
              <th>Code</th>
              <th>Type</th>
              <th>Value</th>
              <th>Start Date</th>
              <th>Expiry Date</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let promo of promotions">
              <td>
                <strong>{{ promo.title || "N/A" }}</strong>
                <div *ngIf="promo.description" class="small text-muted">
                  {{ promo.description }}
                </div>
              </td>
              <td>
                <code>{{ promo.code || "" }}</code>
              </td>
              <td>{{ promo.promotionType || "DISCOUNT" }}</td>
              <td>
                <span
                  *ngIf="
                    promo.promotionType === 'DISCOUNT' || !promo.promotionType
                  "
                  >{{ promo.discount }}%</span
                >
                <span *ngIf="promo.promotionType === 'EXTRA_MILEAGE'"
                  >{{ promo.extraMileage }} km</span
                >
                <span *ngIf="promo.promotionType === 'EXTRA_DAYS'"
                  >{{ promo.extraDays }} days</span
                >
                <span *ngIf="promo.promotionType === 'OTHER_AWARD'"
                  >Special</span
                >
              </td>
              <td>{{ promo.startDate | date : "dd/MM/yyyy" }}</td>
              <td>{{ promo.expiryDate | date : "dd/MM/yyyy" }}</td>
              <td>
                <span
                  [ngClass]="{
                    'badge btn badge-success': promo.status === 'ACTIVE',
                    'badge btn badge-secondary': promo.status === 'INACTIVE',
                    'badge btn badge-danger': promo.status === 'CANCELLED',
                  }"
                >
                  {{ promo.status }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </ng-container>
  </div>
  <!-- <div class="modal-footer">
    <a routerLink="/{{ userType }}/promo-codes"  (click)="modal.dismiss()"class="btn btn-primary mr-2">Go to Promotions</a>
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">Close</button>
  </div> -->
</ng-template>
