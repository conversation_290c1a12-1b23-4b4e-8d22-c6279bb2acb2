import { Component, OnInit, resource, effect } from "@angular/core";
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ToastrService } from "ngx-toastr";
import { AgencyService } from "src/app/shared/services/agency.service";
import { StorageService } from "src/app/shared/services/storage.service";
import { CurrencyService } from "src/app/shared/services/currency.service";
import { FileService } from "src/app/shared/services/file.service";
import { CompressImageService } from "src/app/shared/utilities/compress-image";
import { HttpResponse } from "@angular/common/http";
import { take } from "rxjs/operators";
import { lastValueFrom } from "rxjs";

@Component({
  selector: "app-profile-completion-modal",
  templateUrl: "./profile-completion-modal.component.html",
  styleUrls: ["./profile-completion-modal.component.css"],
  standalone: false,
})
export class ProfileCompletionModalComponent implements OnInit {
  profileForm: UntypedFormGroup;
  agencyId: number;
  loading = false;
  step = 1; // 1: Currency, 2: Contact Details, 3: Logo (optional)
  totalSteps = 3;
  agencyLogo: File | null = null;
  logoPreview: string | null = null;

  // Angular resource for currencies
  currenciesResource = resource({
    loader: async () => lastValueFrom(this.currencyService.getCurrencies()),
  });

  // Resource properties
  loadingCurrencies = this.currenciesResource.isLoading;
  supportedCurrencies = this.currenciesResource.value;
  currencyError = this.currenciesResource.error;

  constructor(
    private fb: UntypedFormBuilder,
    private activeModal: NgbActiveModal,
    private toast: ToastrService,
    private agencyService: AgencyService,
    private storageService: StorageService,
    private currencyService: CurrencyService,
    private fileService: FileService,
    private compressImage: CompressImageService,
  ) {
    // Effect to handle currency loading errors
    effect(() => {
      if (this.currencyError()) {
        console.error("Failed to load supported currencies:", this.currencyError());
        this.toast.error("Failed to load supported currencies. Please try again later.");
      }
    });
  }

  ngOnInit(): void {
    this.agencyId = this.storageService.decrypt(localStorage.getItem("agentId"));

    this.profileForm = this.fb.group({
      baseCurrency: ["", Validators.required],
      telephone: [""],
      address: this.fb.group({
        street: [""],
        city: [""],
        state: [""],
        postalCode: [""],
        country: [""],
      }),
      logo: [""],
    });
  }

  selectCurrency(currencyCode: string) {
    this.profileForm.patchValue({
      baseCurrency: currencyCode,
    });
  }

  getCurrentCurrencyInfo() {
    const currentCode = this.profileForm.value.baseCurrency;
    const currencies = this.supportedCurrencies();
    return (
      currencies?.find((c) => c.code === currentCode) || {
        code: currentCode,
        name: currentCode,
        symbol: currentCode,
      }
    );
  }

  getDisplayCurrencies() {
    return this.supportedCurrencies() || [];
  }

  nextStep() {
    if (this.step === 1) {
      // Validate currency selection
      if (!this.profileForm.value.baseCurrency) {
        this.toast.warning("Please select a base currency");
        return;
      }
    }

    if (this.step < this.totalSteps) {
      this.step++;
    }
  }

  previousStep() {
    if (this.step > 1) {
      this.step--;
    }
  }

  skipStep() {
    if (this.step < this.totalSteps) {
      this.step++;
    } else {
      this.completeProfile();
    }
  }

  async completeProfile() {
    if (!this.profileForm.value.baseCurrency) {
      this.toast.warning("Base currency is required");
      return;
    }

    this.loading = true;

    try {
      // Upload logo first if provided
      if (this.agencyLogo) {
        await this.uploadLogo();
      }

      // Prepare profile data for comprehensive update
      const profileData = {
        baseCurrency: this.profileForm.value.baseCurrency,
        telephone: this.profileForm.value.telephone,
        address: this.profileForm.value.address,
      };

      // Update all profile information using the comprehensive update method
      this.agencyService.updateAgencyProfile(this.agencyId, profileData).subscribe(
        () => {
          this.finishSetup();
        },
        (err) => {
          this.loading = false;
          this.handleError(err);
        },
      );
    } catch (error) {
      this.loading = false;
      this.toast.error("Failed to upload logo");
      console.error("Logo upload error:", error);
    }
  }

  private finishSetup() {
    this.loading = false;
    this.toast.success("Profile setup completed successfully!");

    // Store the selected base currency in local storage
    const selectedCurrency = this.profileForm.value.baseCurrency;
    if (selectedCurrency) {
      this.currencyService.storeBaseCurrency(selectedCurrency);
    }

    this.activeModal.close("completed");
  }

  private handleError(err: any) {
    if (err.status == 0) {
      this.toast.error("Network Connection Failure");
    } else if (err.error?.message) {
      this.toast.error(err.error.message);
    } else if (err.status == 500) {
      this.toast.error("Internal Server Error");
    } else {
      this.toast.error("Failed to update profile");
    }
  }

  dismiss() {
    this.activeModal.dismiss("dismissed");
  }

  getStepTitle() {
    switch (this.step) {
      case 1:
        return "Select Base Currency";
      case 2:
        return "Contact Information";
      case 3:
        return "Agency Logo";
      default:
        return "Complete Profile";
    }
  }

  getStepDescription() {
    switch (this.step) {
      case 1:
        return "Choose your agency's base currency for vehicle pricing and invoicing.";
      case 2:
        return "Add your contact details and address (optional).";
      case 3:
        return "Upload your agency logo to personalize your profile (optional).";
      default:
        return "Finish setting up your profile.";
    }
  }

  getProgressPercentage(): number {
    return Math.round((this.step / this.totalSteps) * 100);
  }

  onLogoChange(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      console.log(`Image size before compressed: ${file.size} bytes.`);
      this.compressImage
        .compress(file)
        .pipe(take(1))
        .subscribe((compressedImage) => {
          console.log(`Image size after compressed: ${compressedImage.size} bytes.`);
          this.agencyLogo = compressedImage;

          // Create preview URL
          const reader = new FileReader();
          reader.onload = (e: any) => {
            this.logoPreview = e.target.result;
          };
          reader.readAsDataURL(compressedImage);
        });
    }
  }

  removeLogo() {
    this.agencyLogo = null;
    this.logoPreview = null;
    // Reset file input
    const fileInput = document.getElementById("logoFile") as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  }

  uploadLogo(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.agencyLogo) {
        resolve();
        return;
      }

      this.fileService.agencyUploadLogo(this.agencyLogo, this.agencyId).subscribe(
        (event) => {
          if (event instanceof HttpResponse) {
            console.log("Logo upload successful");
          }
          resolve();
        },
        (error) => {
          console.error("Logo upload failed:", error);
          reject(error);
        },
      );
    });
  }
}
