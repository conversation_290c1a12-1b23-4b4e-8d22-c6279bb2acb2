import {
  Component,
  OnChanges,
  Output,
  EventEmitter,
  SimpleChanges,
  TemplateRef,
  input,
  effect,
  signal,
  computed,
  inject,
  resource,
} from "@angular/core";
import { VehicleBookingService } from "src/app/shared/services/vehicle-booking.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { StorageService } from "src/app/shared/services/storage.service";
import { AuthService } from "src/app/shared/services/auth.service";
import { environment } from "src/environments/environment";
import { Router } from "@angular/router";
import { lastValueFrom } from "rxjs";
// import { VehicleBooking } from 'src/app/shared/models/VehicleResponse';
import { Page } from "src/app/shared/models/page.model";
import { VehicleBooking } from "src/app/shared/models/VehicleBookingResponse";
import { EmailType, EmailTypeUtil } from "src/app/shared/models/email-type.enum";
import { ToastrService } from "ngx-toastr";
import { CurrencyService } from "src/app/shared/services/currency.service";
import { CurrencyResolutionService } from "src/app/shared/services/currency-resolution.service";

@Component({
  selector: "app-bookings-table",
  templateUrl: "./vehicle-bookings-table-ui.component.html",
  styleUrls: ["./vehicle-bookings-table.component.css"],
  standalone: false,
})
export class VehicleBookingsTableComponent implements OnChanges {
  // Use inject for services
  private router = inject(Router);
  private authService = inject(AuthService);
  private bookingService = inject(VehicleBookingService);
  private modalService = inject(NgbModal);
  private storageService = inject(StorageService);
  private toast = inject(ToastrService);
  private currencyService = inject(CurrencyService);
  private currencyResolutionService = inject(CurrencyResolutionService);

  refreshBookings = this.refreshPage.bind(this);
  updatedBookings = input(false);
  // Convert @Input properties to signals
  agencyFilter = input<string>("");
  searchCriteria = input<string>("");
  clientFilter = input<string>("");
  startDateFilter = input<string>("");
  endDateFilter = input<string>("");
  transportStatus = input<string>("");
  log = input<any[]>([]); // Receives filtered bookings
  selectedTab = input<string>(""); // Receives selectedTab from Parent
  // searchTerm = input<string>(''); // Receives search term from Parent
  start = input<Date | null>(null);
  end = input<Date | null>(null);

  @Output() updated = new EventEmitter<boolean>();

  selectedBookings: any[] = [];
  selectedJob: any;
  isLoading: boolean = false; // Loading state
  page: Page<VehicleBooking>;
  selectedVehicle: any;
  userType: any;
  bookingDetails: any;
  errorMessage: null;
  // bookingId: any;
  vehicles: any[] = []; // Vehicles displayed in the table
  allVehicles: any[] = []; // All vehicles fetched
  baseCurrency: string | null;

  // Email types for dropdown
  emailTypes = EmailTypeUtil.getValues().map((type) => ({
    value: type,
    label: EmailTypeUtil.getDisplayName(EmailType[type]),
  }));

  // State signals
  pageNumber = signal(0);
  pageSize = signal(25);
  sortBy = signal<string>("id");
  directionAsc = signal(false);

  // Computed values for resource request
  resourceParams = computed(() => ({
    selectedTab: this.selectedTab(),
    agentId: this.storageService.decrypt(localStorage.getItem("agentId")) ?? "",
    pageNumber: this.pageNumber(),
    pageSize: this.pageSize(),
    start: this.start() != null ? new Date(this.start())?.toISOString() : "",
    end: this.end() != null ? new Date(this.end())?.toISOString() : "",
    searchCriteria: this.searchCriteria(),
    sortBy: this.sortBy(),
    directionAsc: this.directionAsc(),
  }));

  // Create resource for vehicle booking status
  bookingsResource = resource({
    request: this.resourceParams,
    loader: ({ request }) =>
      lastValueFrom(
        this.bookingService.getBookings(
          request.selectedTab,
          request.agentId,
          request.pageNumber,
          request.pageSize,
          request.start,
          request.end,
          request.searchCriteria,
          request.sortBy,
          request.directionAsc,
        ),
      ),
  });

  // Method to refresh the resource
  refreshResource(): void {
    // Angular 19 uses invalidate() instead of refetch()
    this.bookingsResource.reload();
  }

  constructor() {
    this.userType = this.authService.getUserType();
    this.baseCurrency = this.currencyService.getBaseCurrency();

    // Effect for handling updates
    effect(() => {
      if (this.updatedBookings()) {
        this.updated.emit(true);
        this.refreshResource();
      }
    });

    // Effect to update page when resource changes
    effect(() => {
      if (this.bookingsResource.value()) {
        this.page = this.bookingsResource.value();
      }
    });
  }

  /**
   * Get the currency text to display in table headers
   * Delegates to CurrencyResolutionService for consistent logic
   * @returns The header currency text
   */
  getHeaderCurrency(formatted = true, dataItem?: any): string {
    return this.currencyResolutionService.getHeaderCurrency(formatted, dataItem);
  }

  ngOnChanges(_changes: SimpleChanges): void {
    this.page = null;
    this.refreshResource();
  }

  sort(sortByValue: string) {
    this.pageNumber.set(0);
    if (this.sortBy() === sortByValue) this.directionAsc.update((value) => !value);
    this.sortBy.set(sortByValue);
    this.refreshResource();
  }

  debitNote(ref: String) {
    this.userType == "agency"
      ? this.router.navigate(["/agency/billing/invoice/" + ref])
      : this.router.navigate(["/admin/billing/invoicing/" + ref]);
  }

  // The getVehicleBookingStatus method is now replaced by the bookingsResource

  daysHired(booking: VehicleBooking): number {
    if (booking.invoices && booking.invoices.length > 0) {
      const rentalItems = booking.invoices[0].invoiceItemResult.filter((item) =>
        item.description.includes("Car rental for"),
      );
      if (rentalItems.length > 0) {
        return rentalItems.length;
      }
    }

    // Default fallback
    return 1;
  }

  handlePageChange(event: "next" | "prev") {
    if (event == "next") {
      this.pageNumber.update((value) => value + 1);
    } else if (event == "prev") {
      this.pageNumber.update((value) => value - 1);
    }
    this.refreshResource();
  }

  resetPageNumber() {
    this.pageNumber.set(0);
  }

  // Helper function to calculate days hired

  // View Details: Open modal
  viewDetails(vehicles: any, modalContent: TemplateRef<any>): void {
    this.selectedVehicle = vehicles;
    this.modalService.open(modalContent, {
      ariaLabelledBy: "modal-basic-title",
      backdrop: true,
      centered: true,
      windowClass: "handover-modal",
      backdropClass: "handover-modal-backdrop",
      size: "xl",
    });
  }

  getBookingById(id: number): void {
    // Using lastValueFrom to avoid deprecated subscribe method
    lastValueFrom(this.bookingService.getVehicleBookingById(id)).then(
      (response) => {
        this.bookingDetails = response;
        this.errorMessage = null;
      },
      (error) => {
        console.error("Error fetching booking details:", error);
      },
    );
  }

  // // Refresh Data After Changes
  refreshPage(_event: any): void {
    this.updated.emit(true);
    this.refreshResource();
  }

  // Selected email type for confirmation
  selectedEmailType: string = null;
  selectedEmailTypeDisplay: string = "";
  isConfirmingEmail: boolean = false;

  // Cancel booking properties
  cancelReason: string = "";
  isReasonRequired: boolean = false;
  isReasonInvalid: boolean = false;

  /**
   * Prepare to resend a specific email for a booking
   * @param booking The booking object
   * @param emailTypeStr The type of email to resend
   */
  prepareResendEmail(booking: any, emailTypeStr: string): void {
    if (!booking || !booking.id) {
      this.toast.error("Invalid booking information");
      return;
    }

    this.selectedEmailType = emailTypeStr;
    this.selectedEmailTypeDisplay = EmailTypeUtil.getDisplayName(EmailType[emailTypeStr]);
    this.isConfirmingEmail = true;
  }

  /**
   * Resend a specific email for a booking after confirmation
   * @param booking The booking object
   */
  confirmResendEmail(booking: any): void {
    if (!booking || !booking.id || !this.selectedEmailType) {
      this.toast.error("Invalid booking or email information");
      return;
    }

    const emailType = EmailType[this.selectedEmailType];
    const displayName = this.selectedEmailTypeDisplay;

    this.toast.info(`Resending ${displayName} email...`);
    this.isConfirmingEmail = false;

    lastValueFrom(this.bookingService.resendBookingEmail(booking.id, emailType))
      .then((response) => {
        this.toast.success(`${displayName} email has been resent successfully`);
      })
      .catch((error) => {
        console.error("Error resending email:", error);
        this.toast.error(`Failed to resend ${displayName} email. Please try again.`);
      });
  }

  /**
   * Cancel the email resend operation
   */
  cancelResendEmail(): void {
    this.selectedEmailType = null;
    this.selectedEmailTypeDisplay = "";
    this.isConfirmingEmail = false;
  }

  /**
   * Determine if an email type should be shown based on user type and booking status
   * @param emailType The email type to check
   * @returns true if the email type should be shown
   */
  shouldShowEmailType(emailType: string): boolean {
    // Admin users can see all email types
    if (this.userType === "admin") {
      return true;
    }

    // Hide certain admin-only email types for non-admin users
    if (emailType === "AGENCY_BOOKING_RECEIVED" || emailType === "BOOKING_PAYMENT_FAILED") {
      return false;
    }

    // Show booking completion email only for completed bookings
    if (emailType === "BOOKING_COMPLETED") {
      return this.selectedVehicle?.status === "COMPLETE";
    }

    // Show booking reminder for booked bookings (not yet started)
    if (emailType === "BOOKING_REMINDER") {
      return this.selectedVehicle?.status === "BOOKED";
    }

    // Show booking cancellation email only for cancelled bookings
    if (emailType === "BOOKING_CANCELLED") {
      return this.selectedVehicle?.status === "CANCELLED";
    }

    // Show booking paid email only for paid/booked bookings
    if (emailType === "BOOKING_PAID") {
      return (
        this.selectedVehicle?.status === "BOOKED" ||
        this.selectedVehicle?.status === "AUTHORIZED" ||
        this.selectedVehicle?.status === "WAITINGAUTH" ||
        this.selectedVehicle?.status === "COMPLETE"
      );
    }

    // Show other email types by default
    return true;
  }

  /**
   * Open email resend modal
   * @param booking The booking object
   * @param modalContent The modal template reference
   */
  openResendEmailModal(booking: any, modalContent: TemplateRef<any>): void {
    this.selectedVehicle = booking;
    this.modalService.open(modalContent, {
      ariaLabelledBy: "modal-basic-title",
      backdrop: true,
      centered: true,
    });
  }

  /**
   * Open actions modal
   * @param booking The booking object
   * @param modalContent The modal template reference
   */
  openActionsModal(booking: any, modalContent: TemplateRef<any>): void {
    this.selectedVehicle = booking;
    this.modalService.open(modalContent, {
      ariaLabelledBy: "modal-basic-title",
      backdrop: true,
      centered: true,
      size: "lg", // Make the modal larger to accommodate the action list
    });
  }

  /**
   * Open cancel booking modal
   * @param booking The booking object
   * @param modalContent The modal template reference
   */
  openCancelBookingModal(booking: any, modalContent: TemplateRef<any>): void {
    this.selectedVehicle = booking;
    this.cancelReason = "";
    this.isReasonInvalid = false;

    // Check if booking is within 48 hours of start time
    const bookingStartDate = new Date(booking.start);
    const now = new Date();
    const hoursUntilBooking = (bookingStartDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    this.isReasonRequired = hoursUntilBooking <= 48;

    this.modalService.open(modalContent, {
      ariaLabelledBy: "modal-basic-title",
      backdrop: "static", // Prevent closing on backdrop click for important actions
      centered: true,
    });
  }

  /**
   * Cancel the cancellation process
   */
  cancelCancellation(): void {
    this.cancelReason = "";
    this.isReasonInvalid = false;
  }

  /**
   * Confirm booking cancellation
   * @param booking The booking to cancel
   */
  confirmCancelBooking(booking: any): void {
    if (!booking || !booking.id) {
      this.toast.error("Invalid booking information");
      return;
    }

    // Validate reason if required
    if (this.isReasonRequired && (!this.cancelReason || this.cancelReason.trim() === "")) {
      this.isReasonInvalid = true;
      return;
    }

    this.toast.info("Processing cancellation...");

    // Determine if cancellation is by agency based on user type
    const cancelledByAgency = this.userType === "agency";

    lastValueFrom(
      this.bookingService.cancelBooking(booking.id, this.cancelReason, cancelledByAgency),
    )
      .then(() => {
        this.toast.success("Booking cancelled successfully");
        this.refreshResource();
        this.cancelReason = "";
        this.isReasonInvalid = false;
      })
      .catch((error) => {
        console.error("Error cancelling booking:", error);
        let errorMessage = "Failed to cancel booking. Please try again.";

        if (error.error && error.error.message) {
          errorMessage = error.error.message;
        }

        this.toast.error(errorMessage);
      });
  }
}
