import { SharedModule } from "./../shared/shared.module";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { AgencyRoutingModule } from "./agency-routing.module";
import { AgencyContainerComponent } from "./agency-container/agency-container.component";
import { AgencyHomeComponent } from "./agency-home/agency-home.component";
import { ShiftsModule } from "../admin/shifts/shifts.module";
import { PipesModule } from "../shared/pipes.module";
import { WorkerComplianceComponent } from "./worker-compliance/worker-compliance.component";
import { MatDialogModule } from "@angular/material/dialog";
import { AgencyJobsModule } from "./agency-jobs/jobs.module";
import { PromoCodeListComponent } from "./promo-codes/promo-code-list.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { SortIconComponent } from "../shared/components/icons/sort-icon.component";
import { NgSelectModule } from "@ng-select/ng-select";
import { MultiInputComponent } from "../shared/components/multi-input/multi-input.component";
import { MatProgressBarModule } from "@angular/material/progress-bar";
import { SharedComponentsModule } from "../shared/components/shared-components.module";
import { BackButtonComponent } from "../shared/components/back-button/back-button.component";
import { ProfileCompletionModalComponent } from "./components/profile-completion-modal/profile-completion-modal.component";
import { PhoneInputComponent } from "../shared/components/phone-input/phone-input.component";
import { CountrySelectorComponent } from "../shared/components/country-selector/country-selector.component";

@NgModule({
  declarations: [
    AgencyContainerComponent,
    PromoCodeListComponent,
    AgencyHomeComponent,
    WorkerComplianceComponent,
    ProfileCompletionModalComponent,
  ],
  imports: [
    CommonModule,
    AgencyRoutingModule,
    SharedModule,
    SharedComponentsModule,
    FormsModule,
    NgSelectModule,
    CommonModule,
    MatProgressBarModule,
    ReactiveFormsModule,
    MatDialogModule,
    AgencyJobsModule,
    PipesModule,
    MultiInputComponent,
    BackButtonComponent,
    PhoneInputComponent,
    CountrySelectorComponent,
  ],
})
export class AgencyModule {}
