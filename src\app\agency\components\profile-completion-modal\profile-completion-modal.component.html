<div class="modal-header bg-main text-white border-0 complete-profile-modal-width">
  <h5 class="modal-title text-center w-100">Complete Your Profile</h5>
</div>

<div class="modal-body complete-profile-modal-width">
  <!-- Progress indicator -->
  <div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <span class="text-muted small">Step {{ step }} of {{ totalSteps }}</span>
      <span class="text-muted small">{{ getProgressPercentage() }}% Complete</span>
    </div>
    <div class="progress" style="height: 4px">
      <div
        class="progress-bar bg-main"
        [style.width.%]="(step / totalSteps) * 100"
        role="progressbar"
      ></div>
    </div>
  </div>

  <!-- Step content -->
  <div class="text-center mb-4">
    <h5 class="mb-2">{{ getStepTitle() }}</h5>
    <p class="text-muted">{{ getStepDescription() }}</p>
  </div>

  <form [formGroup]="profileForm">
    <!-- Step 1: Currency Selection -->
    <div *ngIf="step === 1">
      <div class="mb-4">
        <h6 class="mb-3">Select Your Base Currency</h6>
        <p class="text-muted small mb-3">
          <i class="fas fa-info-circle"></i>
          This will be used for all vehicle pricing and invoicing. You can change this later in
          settings.
        </p>

        <!-- Current selection display -->
        <div class="alert alert-light border mb-3" *ngIf="profileForm.value.baseCurrency">
          <div class="d-flex align-items-center">
            <i class="fas fa-check-circle text-success me-2 mr-2"></i>
            <strong>Selected: {{ getCurrentCurrencyInfo().code }}</strong> -
            {{ getCurrentCurrencyInfo().name }}
          </div>
        </div>

        <!-- Currency selection grid -->
        <div class="row g-2" *ngIf="!loadingCurrencies()">
          <div class="col-6 col-md-4" *ngFor="let currency of getDisplayCurrencies()">
            <button
              type="button"
              class="btn w-100 text-start mb-2"
              [class.btn-primary]="profileForm.value.baseCurrency === currency.code"
              [class.btn-outline-secondary]="profileForm.value.baseCurrency !== currency.code"
              (click)="selectCurrency(currency.code)"
            >
              <div class="d-flex justify-content-between align-items-center">
                <div class="text-left">
                  <strong>{{ currency.code }}</strong>
                  <br />
                  <small class="badge bg-light text-dark">{{ currency.name }}</small>
                </div>
                <span class="badge bg-light text-dark">{{ currency.symbol }}</span>
              </div>
            </button>
          </div>
        </div>

        <!-- Loading state -->
        <div class="text-center py-4" *ngIf="loadingCurrencies()">
          <p class="text-muted mt-2">Loading currencies...</p>
        </div>

        <!-- No currencies available notice -->
        <div
          class="alert alert-warning mt-3"
          *ngIf="getDisplayCurrencies().length === 0 && !loadingCurrencies()"
        >
          <i class="fas fa-exclamation-triangle me-2"></i>
          <strong>No currencies available:</strong>
          Unable to load supported currencies. Please contact support or try again later.
        </div>
      </div>
    </div>

    <!-- Step 2: Contact Information -->
    <div *ngIf="step === 2">
      <div class="mb-4">
        <h6 class="mb-3">Contact Information</h6>
        <p class="text-muted small mb-3">
          <i class="fas fa-info-circle"></i>
          This information is optional but helps clients contact you directly.
        </p>

        <div class="mb-3">
          <label for="telephone" class="form-label">Phone Number</label>
          <phone-input
            id="telephone"
            formControlName="telephone"
            placeholder="Enter your phone number"
          />
        </div>

        <div formGroupName="address">
          <h6 class="mb-2">Address</h6>
          <div class="row g-2">
            <div class="col-6 mb-2">
              <input
                type="text"
                class="form-control"
                formControlName="street"
                placeholder="Street Address"
              />
            </div>
            <div class="col-6 mb-2">
              <input type="text" class="form-control" formControlName="city" placeholder="City" />
            </div>
            <div class="col-6 mb-2">
              <input
                type="text"
                class="form-control"
                formControlName="state"
                placeholder="State/Province"
              />
            </div>
            <div class="col-6 mb-2">
              <input
                type="text"
                class="form-control"
                formControlName="postalCode"
                placeholder="Postal Code"
              />
            </div>
            <div class="col-6 mb-2">
              <country-selector formControlName="country" placeholder="Select country" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 3: Logo Upload -->
    <div *ngIf="step === 3">
      <div class="mb-4">
        <h6 class="mb-3">Agency Logo</h6>
        <p class="text-muted small mb-3">
          <i class="fas fa-info-circle"></i>
          Upload your agency logo to personalize your profile. This is optional and can be added
          later.
        </p>

        <div class="text-center">
          <!-- Logo Preview or Upload Area -->
          <div class="border rounded p-4 mb-3" style="min-height: 150px">
            <div
              *ngIf="!logoPreview"
              class="d-flex flex-column align-items-center justify-content-center h-100"
            >
              <i class="fas fa-image fa-3x text-muted mb-3"></i>
              <p class="text-muted mb-3">Click to upload your agency logo</p>
              <button
                type="button"
                class="btn btn-outline-primary btn-sm"
                onclick="document.getElementById('logoFile').click()"
              >
                <i class="fas fa-upload me-1"></i>
                Choose File
              </button>
            </div>

            <div *ngIf="logoPreview" class="position-relative">
              <img
                [src]="logoPreview"
                alt="Logo Preview"
                class="img-fluid"
                style="max-height: 120px"
              />
              <button
                type="button"
                class="btn btn-sm btn-danger position-absolute top-0 end-0 m-2"
                (click)="removeLogo()"
                title="Remove logo"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Hidden file input -->
          <input
            type="file"
            id="logoFile"
            (change)="onLogoChange($event)"
            accept="image/png, image/webp, image/svg, image/jpg, image/jpeg"
            style="display: none"
          />

          <!-- Upload instructions -->
          <small class="text-muted">
            Supported formats: PNG, JPG, JPEG, WebP, SVG. Max size: 5MB
          </small>
        </div>
      </div>
    </div>
  </form>

  <div class="">
    <div class="d-flex justify-content-between w-100">
      <!-- Previous button -->
      <button
        type="button"
        class="btn btn-outline-secondary"
        [disabled]="step === 1"
        (click)="previousStep()"
      >
        <i class="fas fa-arrow-left me-1"></i>
        Previous
      </button>

      <div>
        <!-- Skip button (for optional steps) -->
        <button type="button" class="btn btn-secondary me-2" *ngIf="step > 1" (click)="skipStep()">
          Skip
        </button>

        <!-- Next/Complete button -->
        <button
          type="button"
          class="btn btn-primary ml-2"
          [disabled]="loading || (step === 1 && !profileForm.value.baseCurrency)"
          (click)="step === totalSteps ? completeProfile() : nextStep()"
        >
          <span *ngIf="loading" class="spinner-border spinner-border-sm me-1"></span>
          <i *ngIf="!loading && step < totalSteps" class="fas fa-arrow-right me-1"></i>
          <i *ngIf="!loading && step === totalSteps" class="fas fa-check me-1"></i>
          {{ step === totalSteps ? "Complete Setup" : "Next" }}
        </button>
      </div>
    </div>
  </div>
</div>
