<div class="input-group country-selector-dropdown w-100">
  <button
    class="btn btn-lg btn-outline-secondary dropdown-toggle rounded-left w-100"
    type="button"
    (click)="toggleDropdown()"
    [attr.aria-expanded]="isDropdownOpen()"
    [disabled]="isDisabled()"
  >
    @if (selectedCountry()) {
      <span class="mr-2">{{ selectedCountry()?.flag }}</span>
      <span>{{ selectedCountry()?.name }}</span>
    } @else {
      <span>Select country</span>
    }
  </button>
  <div class="dropdown-menu w-100" [class.show]="isDropdownOpen()">
    <div class="search-container w-100">
      <input
        #searchInput
        type="text"
        class="form-control form-control-sm w-100"
        placeholder="Search countries..."
        [value]="searchTerm()"
        (input)="onSearchChange($event)"
        (click)="$event.stopPropagation()"
      />
    </div>
    <div role="separator" class="dropdown-divider"></div>
    <div class="dropdown-items-container">
      @if (filteredCountries().length === 0) {
        <div class="dropdown-item-text text-muted">No countries found</div>
      }
      @for (country of filteredCountries(); track country.isoCode) {
        <button type="button" class="dropdown-item country-item" (click)="selectCountry(country)">
          <span class="mr-2">{{ country.flag }}</span>
          <span class="country-name">{{ country.name }}</span>
        </button>
      }
    </div>
  </div>
</div>
