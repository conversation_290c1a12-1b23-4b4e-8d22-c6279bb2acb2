import { Pipe, PipeTransform, inject } from '@angular/core';
import { CurrencyService } from '../services/currency.service';
import { CurrencyResolutionService } from '../services/currency-resolution.service';

@Pipe({
  name: 'contextualCurrency',
  pure: true, // Pure pipe since we're passing context as parameter
  standalone: false
})
export class ContextualCurrencyPipe implements PipeTransform {
  private currencyService = inject(CurrencyService);
  private currencyResolutionService = inject(CurrencyResolutionService);

  /**
   * Transform amount using contextual currency resolution
   * @param amount - The amount to format
   * @param dataItem - Optional booking/vehicle/invoice object containing currency context
   * @returns Formatted currency string
   */
  transform(amount: number, dataItem?: any): string {
    if (amount === null || amount === undefined) {
      return '';
    }

    // Get the current user type
    const userType = this.currencyResolutionService.getCurrentUserType();
    
    // Resolve the appropriate currency for this context
    const currency = this.currencyResolutionService.getCurrencyForContext(userType, dataItem);
    
    // Format using the resolved currency
    return this.currencyService.formatCurrencyIntl(amount, currency);
  }
}