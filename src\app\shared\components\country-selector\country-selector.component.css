.input-group.country-selector-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-menu {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 1000;
  width: 100%;
}

.country-item {
  display: flex;
  align-items: center;
  white-space: normal;
  word-break: break-word;
}

.search-container {
  padding: 0.5rem;
  width: 100%;
}

.search-container input {
  width: 100%;
}

.dropdown-items-container {
  width: 100%;
}

.country-name {
  flex: 1 1 auto;
}

.ml-auto {
  margin-left: auto;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.country-selector-dropdown .btn {
  text-align: left;
  padding-right: 1.5rem;
}

.dropdown-toggle {
  padding-inline: 0.75rem !important;
  padding-block: 0.375rem !important;
}

.country-selector-dropdown .dropdown-toggle::after {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
}
