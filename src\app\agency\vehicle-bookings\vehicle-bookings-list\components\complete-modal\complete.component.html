<div>
  <div class="px-3 pt-3">
    <button
      class="btn btn-primary"
      type="button"
      (click)="downloadPDF()"
      [disabled]="isGeneratingPDF()"
    >
      {{ isGeneratingPDF() ? "Generating PDF..." : "Download PDF" }}
    </button>
  </div>
  <div id="complete-form">
    <div class="container mt-5">
      <form *ngIf="returnForm" [formGroup]="returnForm" class="complete-form">
        <div id="formContent" class="container mt-4">
          <div class="row align-items-center justify-content-between">
            <!-- Left Section: Title -->
            <div class="col-5">
              <h4 class="fw-bold">Vehicle Condition Report Form - OUT</h4>
            </div>

            <div class="col-2">
              <div class="d-flex mx-auto align-items-center">
                <img
                  src="../../../../../../assets/images/Vector.png"
                  alt="KarLink Logo"
                  class="img-fluid logo"
                />
              </div>
            </div>
            <div class="col">
              <div class="d-flex align-items-center mr-4">
                <img
                  src="../../../../../../assets/images/Group 797.png"
                  alt="No Smoking"
                  class="img-fluid restriction-icon"
                />
              </div>
              <p class="mb-0 ms-2">No smoking in car</p>
            </div>

            <div class="col">
              <div class="d-flex align-items-center">
                <img
                  src="../../../../../../assets/images/Group 795.png"
                  alt="No Animals"
                  class="img-fluid restriction-icon"
                />
              </div>
              <p class="mb-0 ms-2">No animals in car</p>
            </div>
          </div>

          <hr />

          <div class="container mt-4">
            <!-- Customer and Vehicle Information Card -->
            <div class="card bg-light p-3 mb-3">
              <div class="row">
                <!-- Booking Details Section -->
                <div class="col-md-4">
                  <h5 class="text-core-blue font-weight-bold mb-3">Booking Details</h5>
                  <p class="mb-1">
                    <strong>Booking ID: </strong>

                    <span class="fw-bold">{{ log.id }}</span>
                  </p>
                  <p class="mb-1">
                    <strong>Booking Start Date: </strong>
                    <span class="fw-bold">{{ log.start | date: "d MMMM y hh:mm a" }}</span>
                  </p>
                  <p class="mb-1">
                    <strong>Booking End Date: </strong>
                    <span class="fw-bold">{{ log.end | date: "d MMMM y hh:mm a" }}</span>
                  </p>
                </div>

                <!-- Hirer Details Section -->
                <div class="col-md-4">
                  <h5 class="text-core-blue font-weight-bold mb-3">Hirer Details</h5>
                  <p class="mb-1">
                    <strong>Name: </strong>
                    <span class="fw-bold">{{ log.firstname }} {{ log.surname }}</span>
                  </p>
                  <p class="mb-1">
                    <strong>Email: </strong>
                    <span class="fw-bold">{{ log.email }}</span>
                  </p>
                  <p class="mb-1">
                    <strong>Contact Number: </strong>
                    <span class="fw-bold">{{ log.phone }}</span>
                  </p>
                </div>

                <!-- Vehicle Details Section -->
                <div class="col-md-4">
                  <h5 class="text-core-blue mb-3 font-weight-bold">Vehicle Details</h5>
                  <p class="mb-1">
                    <strong>Make & Model: </strong>
                    <span class="fw-bold">{{ log.vehicle?.name }} {{ log?.vehicle.model }}</span>
                  </p>
                  <p class="mb-1">
                    <strong>Body Type: </strong>
                    <span class="fw-bold">{{ log.vehicle?.type }}</span>
                  </p>
                  <p class="mb-1">
                    <strong>Reg Number: </strong>
                    <span class="fw-bold">{{ log.vehicle?.regno }}</span>
                  </p>
                </div>
              </div>
            </div>

            <!-- Check In Items Card -->
            <div class="card bg-light p-3 mb-3">
              <div class="row">
                <div class="col-md-3 text-end pe-3 border-end">
                  <h5 class="text-core-blue font-weight-bold">Check in items</h5>
                </div>
              </div>
              <div class="inventory-items-grid" formArrayName="items">
                <div
                  *ngFor="let item of items.controls; let i = index"
                  [formGroupName]="i"
                  class="inventory-item"
                >
                  <!-- System-checked box (disabled) -->
                  <input type="checkbox" formControlName="systemChecked" disabled />
                  <label class="m-0">{{ item.get("name")?.value }}</label>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-6">
              <div class="fuel-container d-flex align-items-center justify-content-between">
                <!-- Left Section: Fuel Level Label -->
                <div class="col fuel-label text-muted">
                  <span>Fuel level Out</span>
                </div>

                <!-- Middle Section: Slider -->
                <div class="col slider-container text-center">
                  <p class="slider-text">Move slider</p>
                  <div class="slider-wrapper">
                    <input
                      type="range"
                      min="0"
                      max="10"
                      step="1"
                      id="fractionSlider"
                      [value]="log.fuelOut"
                      class="fuel-slider"
                      [disabled]="isSliderDisabled"
                    />
                    <div class="slider-labels">
                      <span class="text-danger">0</span>
                      <span>1</span>
                      <span>2</span>
                      <span>3</span>
                      <span>4</span>
                      <span>5</span>
                      <span>6</span>
                      <span>7</span>
                      <span>8</span>
                      <span>9</span>
                      <span class="text-success">10</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-6">
              <div class="fuel-container d-flex align-items-center justify-content-between">
                <!-- Left Section: Fuel Level Label -->
                <div class="fuel-label text-muted">
                  <span>Fuel level In</span>
                </div>

                <!-- Middle Section: Slider -->
                <div class="col-6 slider-container text-center">
                  <p class="slider-text">Move slider</p>
                  <div class="slider-wrapper">
                    <input
                      type="range"
                      min="0"
                      max="10"
                      step="1"
                      id="fractionSlider"
                      [value]="log.fuelIn"
                      class="fuel-slider"
                      [disabled]="isSliderDisabled"
                    />
                    <div class="slider-labels">
                      <span class="text-danger">0</span>
                      <span>1</span>
                      <span>2</span>
                      <span>3</span>
                      <span>4</span>
                      <span>5</span>
                      <span>6</span>
                      <span>7</span>
                      <span>8</span>
                      <span>9</span>
                      <span class="text-success">10</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <br />
          <br />

          <div class="row">
            <div class="col">
              <div class="row fuel-container d-flex align-items-center justify-content-between">
                <div class="col-3">
                  <label for="mileageOut" class="form-label fw-light text-muted">Mileage Out</label>
                  <input
                    name="mileageIn"
                    type="number"
                    [value]="log.mileageOut"
                    class="form-control"
                    placeholder="Enter mileage"
                  />
                </div>
                <div class="col d-flex align-items-center">
                  <span class="text-muted ms-2">
                    &#9432; The hirer is responsible for all fuel used once leaving our branch<br />
                    @let limit = getReturnMileageLimit();
                    @if (limit === 0) {
                      There is no return mileage limit
                    } @else {
                      The return mileage limit is
                      <strong>{{ getReturnMileageLimit() }}km</strong>
                    }
                  </span>
                </div>
                <div class="col-3">
                  <label for="mileageIn" class="form-label fw-light text-muted">Mileage In</label>
                  <input
                    name="mileageIn"
                    type="number"
                    [value]="log.mileageIn"
                    class="form-control"
                  />
                </div>
              </div>
            </div>
            <!-- Mileage Input Section -->
            <br />

            <br />

            <div class="image-container">
              <img [src]="log.damageImageIn" class="w-100" />

              <!-- Comment Markers -->
              <!-- <div
                      *ngFor="let comment of comments"
                      class="comment-marker"
                      [style.left.px]="comment.x"
                      [style.top.px]="comment.y"
                      (click)="selectComment(comment, $event)"
                    >
                      {{ comments.indexOf(comment) + 1 }}
                    </div>

                  <div
                  *ngIf="newCommentPosition"
                  class="comment-input"
                  [style.left.px]="newCommentPosition.x + 20"
                  [style.top.px]="newCommentPosition.y"
                  >
                  <div class="form-group">
                    <textarea
                      formControlName="newCommentText"
                      class="form-control"
                      placeholder="Add your comment..."
                      rows="3"
                      [(ngModel)]="newCommentText"
                    ></textarea>
                  </div>
                      <button
                        type="button"
                        class="btn btn-primary btn-sm mr-2"
                        (click)="addComment()"
                        [disabled]="!newCommentText?.trim()"
                      >
                        Add Comment
                      </button>
                      <button
                        type="button"
                        class="btn btn-secondary btn-sm"
                        (click)="cancelNewComment()"
                      >
                        Cancel
                      </button>
                    </div>

                  </div> -->
              <!-- Disclaimer -->
            </div>

            <div class="row">
              <div class="col">
                <div class="row fuel-container d-flex align-items-center justify-content-between">
                  <strong>
                    In keeping with the industry standards, short term vehicle rental does not have
                    a margin of fair wear and tear for damage. Therefore, any change to the
                    condition of the vehicle during the rental is the responsibility of the
                    customer.
                  </strong>
                </div>
              </div>
            </div>

            <div class="container">
              <div class="row">
                <!-- Check-out Damage Column -->
                <div class="col-6">
                  <h6 class="text-core-blue mb-3">Damage Recorded at Check-out</h6>
                  <div class="table-responsive mt-2">
                    <table class="table table-bordered">
                      <thead class="bg-custom-danger text-white p-2">
                        <tr class="card-header">
                          <th>Area</th>
                          <th>Description (Initial Condition)</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let damage of sortedDamageInfoOut" class="mb-3">
                          <td>
                            <strong>{{ damage.area }}</strong>
                          </td>
                          <td>
                            <strong class="mb-1">{{ damage.description }}</strong>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Check-in Damage Column -->
                <div class="col-6">
                  <h6 class="text-core-blue mb-3">Damage Recorded at Check-in</h6>
                  <div class="table-responsive mt-2">
                    <div
                      *ngIf="
                        (log.damageInfoIn && log.damageInfoIn.length > 0) ||
                        (log.extraCharges && log.extraCharges.length > 0)
                      "
                    >
                      <table class="table table-bordered">
                        <thead class="bg-custom-danger text-white p-2">
                          <tr class="card-header">
                            <th>Area</th>
                            <th>Description (Return Condition)</th>
                            <th>Charge {{ getHeaderCurrency() }}</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let damage of sortedDamageInfoIn">
                            <td>{{ damage.area }}</td>
                            <td>{{ damage.description }}</td>
                            <td>{{ damage.charge | contextualCurrency: log }}</td>
                          </tr>
                          <tr *ngFor="let charge of log.extraCharges">
                            <td>-</td>
                            <td>{{ charge.description }}</td>
                            <td>{{ charge.charge | contextualCurrency: log }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <p *ngIf="!log.damageInfoIn || log.damageInfoIn.length === 0">
                      No recorded damage information.
                    </p>
                  </div>
                </div>
              </div>

              <br />
              <br />
              <br />

              <div class="damage-images-container">
                <h5 class="text-core-blue font-weight-bold mb-0 text-center">
                  Vehicle damage photos
                </h5>
                <div class="damage-images">
                  @for (photo of log.damagePhotos; track photo.id) {
                    <div>
                      <img [src]="photo.url" alt="Damage Preview" />
                    </div>
                  } @empty {
                    <div class="text-center no-damage-photos">
                      Vehicle does not have damage photos
                    </div>
                  }
                </div>
              </div>

              <!-- Signature Section -->
              <div class="card p-3 mb-4">
                <!-- Checkout Signatures -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="mb-3">Vehicle Handover Signatures (OUT)</h5>
                    <div class="row align-items-stretch">
                      <!-- Customer Signature Out -->
                      <div class="col-5 d-flex flex-column">
                        <h6>Customer Signature</h6>
                        <div
                          class="border border-dark rounded p-3 flex-grow-1"
                          style="height: 150px"
                        >
                          <img
                            *ngIf="getCustomerSignatureOut()"
                            [src]="getCustomerSignatureOut()"
                            alt="Customer Signature Out"
                            class="img-fluid h-100"
                            style="object-fit: contain"
                          />
                          <p *ngIf="!getCustomerSignatureOut()" class="text-muted">
                            No signature available
                          </p>
                        </div>
                        <div class="row w-100 mt-2 px-3">
                          <div class="d-flex flex-column">
                            <span>{{
                              getCustomerNameOut() || log.firstname + " " + log.surname
                            }}</span>
                            <span>{{ log.signatureOutDate | date }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Center Vertical Line -->
                      <div class="col-2 d-flex justify-content-center">
                        <svg width="2" height="100%">
                          <line
                            x1="0"
                            y1="0"
                            x2="0"
                            y2="100%"
                            style="stroke: black; stroke-width: 2"
                          />
                        </svg>
                      </div>

                      <!-- Representative Signature Out -->
                      <div class="col-5 d-flex flex-column">
                        <h6>Representative Signature</h6>
                        <div
                          class="border border-dark rounded p-3 flex-grow-1"
                          style="height: 150px"
                        >
                          <img
                            *ngIf="getRepresentativeSignatureOut()"
                            [src]="getRepresentativeSignatureOut()"
                            alt="Representative Signature Out"
                            class="img-fluid h-100"
                            style="object-fit: contain"
                          />
                          <p *ngIf="!getRepresentativeSignatureOut()" class="text-muted">
                            No signature available
                          </p>
                        </div>
                        <div class="row w-100 mt-2 px-3">
                          <div class="d-flex flex-column">
                            <span>{{ getRepresentativeNameOut() || "N/A" }}</span>
                            <span>{{ log.signatureOutDate | date }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Check-in Signatures -->
                <div class="row">
                  <div class="col-12">
                    <h5 class="mb-3">Vehicle Return Signatures (IN)</h5>
                    <div class="row align-items-stretch">
                      <!-- Customer Signature In -->
                      <div class="col-5 d-flex flex-column">
                        <h6>Customer Signature</h6>
                        <div
                          class="border border-dark rounded p-3 flex-grow-1"
                          style="height: 150px"
                        >
                          <img
                            *ngIf="getCustomerSignatureIn()"
                            [src]="getCustomerSignatureIn()"
                            alt="Customer Signature In"
                            class="img-fluid h-100"
                            style="object-fit: contain"
                          />
                          <p *ngIf="!getCustomerSignatureIn()" class="text-muted">
                            No signature available
                          </p>
                        </div>
                        <div class="row w-100 mt-2 px-3">
                          <div class="d-flex flex-column">
                            <span>{{
                              getCustomerNameIn() || log.firstname + " " + log.surname
                            }}</span>
                            <span>{{ log.signatureInDate | date }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- Center Vertical Line -->
                      <div class="col-2 d-flex justify-content-center">
                        <svg width="2" height="100%">
                          <line
                            x1="0"
                            y1="0"
                            x2="0"
                            y2="100%"
                            style="stroke: black; stroke-width: 2"
                          />
                        </svg>
                      </div>

                      <!-- Representative Signature In -->
                      <div class="col-5 d-flex flex-column">
                        <h6>Representative Signature</h6>
                        <div
                          class="border border-dark rounded p-3 flex-grow-1"
                          style="height: 150px"
                        >
                          <img
                            *ngIf="getRepresentativeSignatureIn()"
                            [src]="getRepresentativeSignatureIn()"
                            alt="Representative Signature In"
                            class="img-fluid h-100"
                            style="object-fit: contain"
                          />
                          <p *ngIf="!getRepresentativeSignatureIn()" class="text-muted">
                            No signature available
                          </p>
                        </div>
                        <div class="row w-100 mt-2 px-3">
                          <div class="d-flex flex-column">
                            <span>{{ getRepresentativeNameIn() || "N/A" }}</span>
                            <span>{{ log.signatureInDate | date }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div></div>

                <!-- Agreement Text -->
                <div class="row">
                  <div class="col">
                    <div
                      class="row fuel-container d-flex align-items-center justify-content-between"
                    >
                      <div class="col">
                        <p>
                          Vehicle provider to agree that the above report is a true record of the
                          received condition and has understood
                          <a href="" class="text-primary">the vehicle handover agreement terms.</a>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card mt-2">
                  <div class="card-body text-center">
                    <div class="form-check">
                      <input
                        type="checkbox"
                        class="form-check-input"
                        id="acknowledgeTerms"
                        name="acknowledgeTerms"
                        checked
                      />
                      <label for="acknowledgeTerms" class="form-check-label">
                        I acknowledge and accept the condition the vehicle has been returned in
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Trigger Button -->
          <div>
            <div class="text-center mb-2">
              <button
                type="submit"
                class="btn btn-primary btn-lg px-5 py-2"
                [disabled]="!returnForm.get('acknowledgeTerms')?.value"
              >
                Complete vehicle check
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>

    <div class="container mt-5">
      <div class="card">
        <div class="card-header">
          <div class="bg-custom-danger text-white p-2">
            <strong>Important Hirer Information - Please Read Carefully</strong>
          </div>
        </div>
        <div class="card-body">
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">During your hire</h6>

            <p>
              In the event of a road traffic accident, or any incident of vehicle damage, you must
              call our Emergency Support Line on
              <strong>+263 779 144 386</strong> <strong>IMMEDIATELY</strong>. Reporting any accident
              within 2 hours of incident is a contractual requirement and failure to do so may
              result in increased hirer costs and forfeiture of deposit.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Breakdown / Mechanical issues</h6>
            <p>
              If you experience a breakdown call you should follow guidelines from the vehicle
              provider if they have recommended roadside assistance, or if you have your own cover,
              you can inform the vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Recovery</h6>
            <p>
              Should the hire vehicle need recovering the hirer is liable for costs, if recovery is
              needed as a result of an accident, negligence or driver misuse.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Onward Travel Following Breakdown</h6>
            <p>
              In the even/t of vehicle recovery, immediate onward travel costs are not covered.
              Unless offered as courtesy service by vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Replacement Vehicle Following Breakdown</h6>
            <p>
              In the event of vehicle recovery, the vehicle provider may offer a replacement
              vehicle. If they cannot, they should reimburse the balance of the remaining rental
              period. Contact support&#64;mykarlink.com.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Lost Key</h6>
            <p>
              In the vehicle key is lost during hire, hirers are liable for a lost key charge as
              specified by the vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Fuel</h6>
            <p>
              Please check the fuel type before filling. If you mis-fuel the vehicle you will be
              liable for all costs incurred in fixing the vehicle.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Rental Extension / Branch Contact</h6>
            <p>
              To request an extension contact the vehicle provider directly. Karlink terms and
              conditions will not be applicable for any extensions granted outside the scope of
              Karlink service provisions.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Condition Inspection</h6>
            <p>
              1) The vehicle provider should inspect the vehicle before making it available to hire.
              2) The hirer must perform their own inspection to satisfy themselves with condition /
              condition report (VCR) before driving. Please take photographs/video of any damage, or
              anything you are unsure of and email support&#64;mykarlink.com and also copy the
              vehicle provider.
            </p>
          </div>
          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Damage Tolerance</h6>
            <p>
              Not all damage is chargeable and only chargeable damage is recorded to the VCR.
              Bodywork scratches 40mm+ are chargeable, including combined accumulation of scratches
              deemed to be from the same incident. Scratches below 40mm are not chargeable. Bodywork
              dents 20mm+ are chargeable. Dents below 20mm are not chargeable. ANY damage to wheels
              or tyres is chargeable. Glass chips greater than 1mm are chargeable. In the case of
              multiple damages to a car then charges are ‘per occurrence’. Exclusions from the
              excess exist. Changes in condition to wheels, tyres, glass, roof, underneath, clutch &
              interior, or any change in condition attributed to negligence or misuse, are not
              limited by your
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Damage Tolerance</h6>
            <p>
              Not all damage is chargeable and only chargeable damage is recorded to the VCR.
              Bodywork scratches 40mm+ are chargeable, including combined accumulation of scratches
              deemed to be from the same incident. Scratches below 40mm are not chargeable. Bodywork
              dents 20mm+ are chargeable. Dents below 20mm are not chargeable. ANY damage to wheels
              or tyres is chargeable. Glass chips greater than 1mm are chargeable. In the case of
              multiple damages to a car then charges are ‘per occurrence’. Exclusions from the
              excess exist. Changes in condition to wheels, tyres, glass, roof, underneath, clutch &
              interior, or any change in condition attributed to negligence or misuse, are not
              limited by your excess liability.
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Glass and Tyre Damage</h6>
            <p>
              In the event of windscreen or tyre damage during your rental, preventing safe driving,
              the hirer is responsible for replacement and payment of these items. Any replacement
              items must be ‘like for like’, meaning the new product must be at least the same
              standard as the one it replaces. This is especially applicable to tyres; they must be
              replaced with the same brand and version.
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Personal Belongings / Lost property</h6>
            <p>The vehicle provider is not responsible for hirer items left in vehicles.</p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Vehicle Returns</h6>
            <p>
              You must ensure your vehicle is returned to the specific vehicle provider agreed
              address. Do NOT give keys or a vehicle to anyone other the issuer or their designated
              staff member.
            </p>
          </div>

          <div class="mb-4">
            <h6 class="text-core-blue mb-3">Smoking in the vehicle / Animals / Car cleanliness</h6>
            <p>Smoking is not permitted in vehicles hired via Karlink.</p>
            <p>
              Hirers are not permitted to allow animals in vehicles hired via Karlink unless agreed
              with vehicle provider.
            </p>
            <p>
              Please return the car in fair condition removing all personal belongings and waste.
            </p>
            <p>
              The vehicle provider may deduct $50 from your deposit for vehicles returned in
              unreasonable condition, such as stained seats, footprints on seats, sand in the
              interior, discarded food or excess dirt.
            </p>
            <p>
              Parking Charge Notices / Traffic Violations / Management Fee are the responsibility of
              the hirer.
            </p>
          </div>
        </div>
        <div class="card-header">
          <div class="bg-custom-danger text-white p-2">
            <strong>Hire Terms and conditions</strong>
          </div>
        </div>

        <div class="card-body">
          <div class="mb-4">
            <p>
              When you sign the rental agreement you accept the conditions below. ‘Provider’ is the
              company/individual referred to as the lessor in the rental agreement and ‘You’ are
              either the company or person referred to as hirer and/or the driver in the rental
              agreement.
            </p>
            <p>
              Please read this agreement carefully. If there is anything you do not understand or
              disagree with, please contact Karlink via support&#64;mykarlink.com.
            </p>
            <br />
            <br />
            <h6>1. Rental Period</h6>
            <p>
              You hire the vehicle for the rental period shown in the agreement. Lessor may agree to
              extend this rental period. Karlink advises such extensions to be processed via Karlink
              website to continue to be covered by the terms and conditions herein.
            </p>
            <p>
              Lessor is entitled to terminate this agreement if you break any of its terms and you
              must then return the vehicle immediately, failure to do so may be reported as theft.
            </p>
            <h6>2. Your Responsiblities</h6>
            <p>
              a) You must look after the vehicle and its keys. You must always lock the vehicle when
              you are not using it, and use any security device supplied with the vehicle. You are
              uninsured in respect of and must personally indemnify the Company for all costs and
              losses, including, but not limited to, recovery costs, losses or repair costs, caused
              by the following matters, for which you are hereby responsible:
            </p>
            <p>b) failure to return keys to the lessor on time or at all;</p>
            <p>
              c) bad weather, as you must always protect the vehicle against weather severe enough
              to cause vehicle damage;
            </p>

            <p>d) failure to use the correct fuel;</p>
            <p>e) failure to maintain correct vehicle fluid levels and tyre pressures;</p>
            <p>
              f) hitting low-level objects, including low tree branches or height-restriction
              devices;
            </p>
            <p>
              g) failure to return the vehicle in the condition in which it was at its pre-rental
              inspection and damage to the exterior and interior of it while in your possession;
              provided that if the damage is covered by insurance, you shall be required to pay the
              cost to the lessor of repair up to the amount of the deposit only.
            </p>
            <p>
              h) loss or damage to equipment supplied (whether hired to you or supplied free of
              charge) at the time of rental, including, but not limited to, satellite navigation
              equipment, baby seats and other goods separate from the vehicle itself.
            </p>
            <p>
              (i) You must not sell, rent, lend or dispose of the whole vehicle or any of its parts.
            </p>
            <p>(ii) You must not give anyone any legal rights over the vehicle.</p>
            <p>
              (iii)You must not let anyone work on the vehicle without permission of the lessor. If
              given permission, you will be responsible for the cost of the work.
            </p>

            <p>
              (iv) You must inform the lessor of any fault in the vehicle as soon as you notice it.
            </p>
            <p>
              (v) You must return the vehicle to the place specified in the rental agreement, at the
              agreed times. You must ensure the lessor inspects the vehicle to check its condition
              on return, and you remain responsible for the vehicle until it has been so checked.
            </p>
            <p>
              (vi) You must return the vehicle empty of all goods and things which you and others
              put into it.
            </p>
            <p>
              (vii) You must not allow any unauthorised person to drive the vehicle. Authorised
              persons are those named on this agreement or authorised by the lessor in writing, or
              corporate hirers who have satisfied the terms of rental that they have suitable
              insurance covering accidents by their staff whom they authorise to drive the vehicle.
            </p>
            <p>
              (viii) You are responsible for all costs incurred through your negligence, in the
              event that the Insurer voids the insurance cover or refuses to indemnify you for any
              reason: for example, damage to clutch.
            </p>
            <p>
              (ix) You must pay the appropriate authority any fines and costs if and when the
              authority demands this payment.
            </p>

            <h6>3. Vehicle provider’s responsibilities.</h6>
            <p>
              The lessor warrant that they have maintained the vehicle to at least the
              manufacturer’s recommended standard, and that it is roadworthy and suitable for
              renting at the start of the rental period. The lessor is responsible to ensure that
              the vehicle is fit to drive; and we has the right to rent it out. Lessor is
              responsible if someone is injured or dies as a result of negligent act or failure to
              act. Lessor is also responsible for losses caused by them breaking this agreement, but
              only such losses as are a foreseeable consequence of them breaking the agreement.
              Losses are foreseeable where they could be contemplated by you and the lessor at the
              time the vehicle is rented.
            </p>
            <p>
              Lessor is not responsible for indirect losses which happen indirectly as a result of
              the main loss or damage and which are not foreseeable by you (such as loss of
              opportunity).
            </p>

            <h6>4. Property.</h6>
            <p>Lessor is not responsible for loss or damage to property left in the vehicle.</p>
            <h6>5. Conditions for using the vehicle.</h6>
            <p>
              The vehicle must only be driven by you and any other driver named in the agreement, or
              by anyone else authorised in writing by the lessor. Anyone driving the vehicle must
              have a full valid driving licence. You or any other authorised driver must not: use
              the vehicle for hire or reward; use the vehicle for any illegal purpose; use the
              vehicle for racing, pace-making, testing the vehicle reliability and speed or taking a
              driving test; use the vehicle while you have alcohol or illegal drugs in your body;
              Use the vehicle while loaded beyond the manufacturer’s maximum weight
            </p>
            <p>
              recommendations; use the vehicle with the load not secured safely; if the vehicle is a
              commercial vehicle, use it for a purpose for which you need an operator licence if you
              do not have one; use the vehicle or allow it to be used off-road or on roads
              unsuitable for it; drive in restricted areas.
            </p>
            <p>
              6. Towing You or any other authorised driver must not use the vehicle for towing
              unless given written permission by the lessor.
            </p>
            <h6>7. Charges.</h6>
            <p>
              (i) The lessor may work out charges using the best quotation from any 3 of their
              choice
            </p>
            <p>(ii) You will pay and indemnify the lessor in respect of the following:</p>
            <p>
              a. The rental and any other charges due under this agreement (unused rental days and
              early returns being non-refundable);
            </p>
            <p>b. Any charge for loss or damage resulting from you breaking condition 2.</p>
            <p>
              c. For fuel consumed and a refuelling charge if you have used, and not replaced, the
              quantity of fuel that was supplied at the start of the original rental.
            </p>
            <p>
              d. The amount of any fines, vehicle clamping costs and court costs for parking
              violations, and other traffic violations;
            </p>
            <p>
              e. KarLink may charge management fees for dealing with any issues in respect of the
              matters in clause 7;
            </p>
            <p>
              f. The reasonable cost of repairing any extra damage which was not noted on the
              vehicle check form at the start of the agreement, whether you were at fault or not
              (subject to clause 3)
            </p>
            <p>
              g. the reasonable cost of replacing the vehicle if stolen or damaged beyond economic
              repair, less any amount recovered from any Insurer under any insurance cover
            </p>

            <p>An excess often excludes specific items, often including damage to</p>
            <p>(i) wheels,</p>
            <p>(ii) tyres,</p>
            <p>
              (iii) glass, which includes headlights, side lights, rear lights and mirror glass;
            </p>
            <p>(iv) the roof and</p>
            <p>(v) the underneath.</p>
            <p>
              h. Any charges arising from any authority seizing the vehicle while in your
              possession, together with a loss-of-income charge while lessor cannot rent out the
              vehicle.
            </p>
            <p>i. Any rates communicated to you for delivering and collecting the vehicle.</p>
            <p>
              j. any fines or costs arising from offences or for any charges demanded by a 3rd party
              as a result of the vehicle being parked or left upon land which is not a public road.
            </p>
            <p>
              m. If for any reason, whether your fault or not, you do not return the vehicle to the
              pre-arranged end of hire location and lessor collects the vehicle then you will pay
            </p>
            <p>(i) all costs for the collection of the vehicle, and</p>
            <p>(ii) compensation for loss of use until collection.</p>
            <h6>8. Lessor should provide FULL COMPREHENSIVE</h6>
            <p>
              insurance and damage protection cover. Lessor may share, but is not obliged to give
              you information about the insurance cover and any restrictions which may apply.
            </p>
            <p>By signing the agreement you are accepting the conditions of the insurance cover.</p>
            <p>
              a. The lessor is required us to have insurance cover for claims arising if you injure
              or kill anybody, or damage property.
            </p>
            <p>b. Lessor insurance cover should provide cover for loss or damage to the vehicle.</p>
            <p>
              c. Lessor insurance cover should provide cover for theft, and damage to the vehicle
              caused during an attempted theft.
            </p>
            <p>
              d. If any person who before the date of the agreement has been convicted of an offence
              relating to driving while having drink or drugs in their body, is driving or in charge
              of the insured vehicle and is proven to the satisfaction of the insurers to be driving
              under the influence of drink or drugs (prescribed or otherwise) at the time of any
              accident, the insurance cover will be limited to that required by the Road Traffic
              Acts.
            </p>
            <h6>9. You may take own insurance cover</h6>
            <p>but this does not replace insurance cover offered by the lessor.</p>
            <p>
              Lessor may ask your insurers to record their name as owners of the vehicle. If the
              vehicle is damaged or stolen you will let the lessor negotiate with the insurers about
              repair of the vehicle and the amount of any payment due. If
            </p>
            <p>
              for any reason the amount which lessor receives from the insurance company is less
              than the amount of any loss suffered, you must pay the difference. You must pay the
              full amount of loss and all costs if the vehicle is damaged, lost or stolen, or a
              claim has been made by any other party, if any insurance policy you have arranged
              fails for any reason whatever.
            </p>
            <h6>10. What to do if you have an accident.</h6>
            <p>
              If you have an accident you Must Not Admit responsibility. You must get the names and
              addresses of everyone involved, including witnesses. You should also make the vehicle
              secure; tell the police straight away if anyone is injured or there is any
              disagreement about who is responsible; and call the lessor from which you rented the
              vehicle within 2 working hours. You must contact Karlink and complete an Accident
              Report Form ("ARF") within 24 hours of the time of the accident.
            </p>
            <p>
              The lessor is not obliged to replace any damaged vehicle with another hire vehicle.
            </p>
            <h6>11. Data Protection You agree that KarLink</h6>
            <p>
              may use any information you have given us to carry out our own market research and you
              authorise us to contact you in the future by the way of direct marketing if we have
              information we feel may be of interest to you. You agree that we can give this
              information to credit reference agencies, debt collectors and any other relevant
              organisation.
            </p>
            <p>
              Motor Insurers share information to prevent fraudulent claims. In the event of a claim
              the information on this form and any claim form may be put on a register and shared
              with others.
            </p>
            <h6>12. Ending the agreement</h6>
            <p>
              a. The lessor or KarLink may end this agreement without prior notice if you break any
              terms of this agreement.
            </p>
            <p>
              b. If you are a company, the lessor or KarLink may end this agreement without notice
              if you go into liquidation; or you call a meeting of creditors, or your goods are
              taken from you to satisfy debts; or you break any of the terms of this agreement.
            </p>
            <p>
              c. If we/the lessor end this agreement, it will not affect our right to claim any
              money we are owed under the agreement. Lessor/KarLink can also claim reasonable costs
              from you if you do not meet any of the terms of this agreement.
            </p>
            <h6>13 Governing law</h6>
            <p>
              This agreement is governed by the laws of the country in which it is signed. Any
              dispute may be settled in its courts.
            </p>
            <p>Last updated 2 January 2025.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <ng-template #signatureModal let-modal>
  <div class="modal-dialog" tabindex="-1" role="dialog" aria-labelledby="signatureModalTitle" aria-hidden="false">
    <div class="modal-content">
      <div class="modal-header">
        <h5 id="signatureModalTitle" class="modal-title">Customer Signature</h5>
        <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
      </div>
      <div class="modal-body">
        <canvas #signatureCanvas width="500" height="200" class="border"></canvas>
      </div>
      <div class="modal-footer">
        <button class="btn btn-danger" (click)="clearSignature()">Clear</button>
        <button type="button" class="btn btn-primary" (click)="saveSignature(modal)">Save</button>
      </div>
    </div>
  </div>
</ng-template> -->

<ng-template #noData>
  <p>No Vehicle data available.</p>
</ng-template>
