import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  input,
  Input,
  OnInit,
  OnDestroy,
  Output,
  signal,
  ViewChild,
  ViewEncapsulation,
  AfterViewInit,
} from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ToastrService } from "ngx-toastr";
import { StorageService } from "src/app/shared/services/storage.service";
import { VehicleBookingService } from "src/app/shared/services/vehicle-booking.service";
import { CommentService } from "src/app/shared/services/comment.service";
import { Comment } from "src/app/shared/services/types";
import html2canvas from "html2canvas";
import { PDFService } from "src/app/shared/services/pdf.service";
import { ClientService } from "src/app/shared/services/client.service";
import { lastValueFrom, Observable, forkJoin, Subject } from "rxjs";
import { DocumentViewerModalComponent } from "src/app/shared/components/document-viewer-modal/document-viewer-modal.component";
import { WebcamImage, WebcamInitError, WebcamComponent } from "ngx-webcam";
import { CurrencyResolutionService } from "src/app/shared/services/currency-resolution.service";

@Component({
  selector: "handover-modal",
  templateUrl: "./handover.component.html",
  styleUrls: ["./handover.component.css"],
  standalone: false,
})
export class HandOverComponent implements OnInit, OnDestroy {
  private activeEditAddressModal: any;
  @Input("teamLeaderView") teamLeaderView = false;
  @Input() dataFromParent!: string;
  @Input() log: any = {}; // Initialize with a default value
  @Output() updated = new EventEmitter<boolean>();
  @Input() inventory: any[];
  @Output() handoverData = new EventEmitter<any>(); // Emits data to the parent
  @ViewChild("signatureCanvas")
  signatureCanvasRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild("imageContainer", { static: false }) imageContainer!: ElementRef;
  @ViewChild("editAddressModal") editAddressModal: any;
  addressForm: FormGroup;
  isUpdatingAddress = signal(false);

  refreshBookings = input<Function>();
  hasSubmitted = signal(false);
  isGeneratingPDF = signal(false);
  agencyId: any;
  vehicleBookingForm: FormGroup;
  signatureOutName: string = "";
  isEditing: boolean = true; // Initially in editing mode

  // Collapsible sections properties
  hirerInfoCollapsed: boolean = true; // Collapsed by default
  termsCollapsed: boolean = true; // Collapsed by default

  // Payment validation properties
  paymentValidated = signal(false);
  isValidatingPayment = signal(false);
  isSkippingPayment = signal(false);

  comments: Comment[] = []; // Holds the list of comments
  newCommentPosition: { x: number; y: number } | null = null;
  newCommentText: string = "";
  selectedComment: Comment | null = null;

  savedSignature: string | null = null; // To store the representative signature as a data URL
  savedCustomerSignature: string | null = null; // To store the customer signature as a data URL
  private canvasContext: CanvasRenderingContext2D | null = null;
  private drawing = false;

  vehicleInventories: any[] = []; // Placeholder for fetched inventories
  today: Date = new Date(); // Add today propert
  acknowledgeTerms: boolean = false;

  constructor(
    private storageService: StorageService,
    private toast: ToastrService,
    private fb: FormBuilder,
    private modalService: NgbModal,
    private bookingService: VehicleBookingService,
    private commentService: CommentService,
    private cdr: ChangeDetectorRef,
    private pdfService: PDFService,
    private clientService: ClientService,
    private currencyResolutionService: CurrencyResolutionService,
  ) {
    this.agencyId = this.storageService.decrypt(localStorage.getItem("agentId"));

    // Initialize address form
    this.addressForm = this.fb.group({
      firstLine: ["", Validators.required],
      town: ["", Validators.required],
      county: ["", Validators.required],
    });

    this.vehicleBookingForm = this.fb.group({
      // Payment validation fields
      amountPaid: [0, [Validators.min(0)]],
      paymentMethod: [""],
      paymentReference: [""],
      depositPaid: [0, [Validators.required, Validators.min(0)]],
      depositMethod: [""],
      depositReference: [""],

      // Original fields
      fuelOut: [0, [Validators.required, Validators.min(0)]],
      id: ["", Validators.required],
      mileageOut: [null, [Validators.required, Validators.min(0)]],
      signatureOutHirer: ["", Validators.required], // Customer/hirer signature
      signatureOutHirerName: [""], // Customer/hirer name (auto-populated)
      signatureOutCarRental: ["", Validators.required], // Car rental representative signature
      signatureOutCarRentalName: ["", Validators.required], // Car rental representative name
      damageInfoOut: this.fb.array([]),
      damageImage: ["", Validators.required],
      checkoutItems: [""], // Use a simple FormControl to store a comma-separated list
      newCommentText: ["", Validators.required], // Add this
      acknowledgeTerms: [false, Validators.requiredTrue],

      firstname: [""],
      surname: [""],
      email: [""],
      phone: [""],
      vehicleId: [""],
      dropoffTime: [""],
      pickupTime: [""],

      discount: [0],
      addons: this.fb.array([]),
      acceptTerms: [false],
    });

    this.commentService.comments$.subscribe((comments) => {
      this.comments = comments;
      this.updateDamageInfoOut(); // Update the form whenever comments change
    });
  }

  getHeaderCurrency(formatted = true) {
    return this.currencyResolutionService.getHeaderCurrency(formatted, this.log);
  }

  ngOnInit(): void {
    this.initForm();

    this.vehicleBookingForm.patchValue({
      signatureOutHirerName: `${this.log.firstname} ${this.log.surname}`,
      mileageOut: this.log.vehicle?.mileage,
      // Set default deposit amount from vehicle
      depositPaid: this.log.vehicle?.depositAmt || 0,
    });

    // Add validation to prevent the user from setting a mileageOut less than the current mileage
    this.vehicleBookingForm
      .get("mileageOut")
      ?.setValidators([Validators.required, Validators.min(this.log.vehicle?.mileage ?? 0)]);

    this.populateVehicleInventories();

    this.vehicleInventories =
      this.log.vehicle?.inventory?.map((inventory) => ({
        ...inventory,
        selected: this.isChecked(inventory.name), // Logic to determine checked status
      })) || [];
  }

  /**
   * Calculate the total amount paid for an invoice
   * @param invoice The invoice object
   * @returns The total amount paid
   */
  getTotalPaid(invoice: any): number {
    if (!invoice || !invoice.payments) {
      return 0;
    }
    return invoice.payments.reduce(
      (total: number, payment: any) => total + (payment.total || 0),
      0,
    );
  }

  /**
   * Calculate the balance due for an invoice
   * @param invoice The invoice object
   * @returns The balance due
   */
  getBalanceDue(invoice: any): number {
    if (!invoice) {
      return 0;
    }
    const totalPaid = this.getTotalPaid(invoice);
    return invoice.totalAmount - totalPaid;
  }

  /**
   * Check if the booking is fully paid
   * @returns True if all invoices are fully paid
   */
  isBookingFullyPaid(): boolean {
    if (!this.log.invoices || this.log.invoices.length === 0) {
      return false;
    }

    // Check if there's any balance due on any invoice
    const totalBalanceDue = this.log.invoices.reduce(
      (total: number, invoice: any) => total + this.getBalanceDue(invoice),
      0,
    );

    // If total balance due is 0 or less, the booking is fully paid
    return totalBalanceDue <= 0;
  }

  /**
   * Check if the payment form is valid
   * @returns True if the payment form is valid
   */
  isPaymentFormValid(): boolean {
    // Get the relevant form controls
    const amountPaid = this.vehicleBookingForm.get("amountPaid");
    const paymentMethod = this.vehicleBookingForm.get("paymentMethod");
    const depositPaid = this.vehicleBookingForm.get("depositPaid");
    const depositMethod = this.vehicleBookingForm.get("depositMethod");

    if (!this.isBookingFullyPaid()) {
      // Require all fields to be set (not null or undefined)
      return (
        amountPaid?.value != null &&
        paymentMethod?.value != null &&
        depositPaid?.value != null &&
        depositMethod?.value != null
      );
    } else {
      // If fully paid, only require deposit fields to be set
      return depositPaid?.value != null && depositMethod?.value != null;
    }
  }

  skipPaymentsCapture(): void {
    this.paymentValidated.set(true);
    this.isValidatingPayment.set(false);
  }

  /**
   * Validate payments and proceed to the vehicle condition section
   */

  validatePayments(): void {
    if (!this.isPaymentFormValid()) {
      this.toast.error("Please complete all required payment fields correctly.");
      return;
    }

    this.isValidatingPayment.set(true);

    // Get form values
    const amountPaid = this.vehicleBookingForm.get("amountPaid")?.value || 0;
    const paymentMethod = this.vehicleBookingForm.get("paymentMethod")?.value;
    const paymentReference = this.vehicleBookingForm.get("paymentReference")?.value;
    const depositPaid = this.vehicleBookingForm.get("depositPaid")?.value || 0;
    const depositMethod = this.vehicleBookingForm.get("depositMethod")?.value;
    const depositReference = this.vehicleBookingForm.get("depositReference")?.value;

    // Check if deposit meets the minimum requirement
    const requiredDeposit = this.log.vehicle?.depositAmt || 0;
    if (depositPaid < requiredDeposit) {
      this.toast.warning(
        `The deposit amount (${depositPaid}) is less than the required amount (${requiredDeposit}).`,
      );
      this.isValidatingPayment.set(false);
      return;
    }

    // Check if there's a balance due and if the amount paid today covers it
    let totalBalanceDue = 0;
    let invoiceId = null;
    if (this.log.invoices && this.log.invoices.length > 0) {
      invoiceId = this.log.invoices[0].id;
      totalBalanceDue = this.log.invoices.reduce(
        (total: number, invoice: any) => total + this.getBalanceDue(invoice),
        0,
      );
    }

    if (amountPaid < totalBalanceDue) {
      this.toast.warning(
        `The amount paid today (${amountPaid}) is less than the total balance due (${totalBalanceDue}).`,
      );
      // Allow to continue anyway after warning
    }

    // Create an array to store our API call observables
    const apiCalls: Observable<any>[] = [];

    // Only record payment if amount is greater than 0 and we have an invoice
    if (amountPaid > 0 && invoiceId) {
      apiCalls.push(
        this.bookingService.recordInvoicePayment(
          invoiceId,
          paymentMethod,
          amountPaid,
          paymentReference,
        ),
      );
    }

    // Always record deposit
    apiCalls.push(
      this.bookingService.recordDeposit(this.log.id, depositPaid, depositMethod, depositReference),
    );

    // Execute all API calls in parallel
    forkJoin(apiCalls).subscribe({
      next: () => {
        this.paymentValidated.set(true);
        this.isValidatingPayment.set(false);
        this.toast.success(
          "Payment and deposit recorded successfully. You can now proceed with the vehicle handover.",
        );
      },
      error: (error) => {
        this.isValidatingPayment.set(false);
        console.error("Error recording payment or deposit:", error);
        this.toast.error("Failed to record payment or deposit. Please try again.");
      },
    });
  }

  /**
   * Skip payment validation and proceed to the vehicle condition section
   * This is used when the booking is already fully paid or when the user wants to skip payment capture
   */
  skipPaymentValidation(): void {
    this.isSkippingPayment.set(true);

    // Show a confirmation dialog if the booking is not fully paid
    if (!this.isBookingFullyPaid()) {
      if (
        !confirm(
          "This booking has an outstanding balance. Are you sure you want to skip payment capture?",
        )
      ) {
        this.isSkippingPayment.set(false);
        return;
      }
    }

    // Set payment as validated without making any API calls
    this.paymentValidated.set(true);
    this.isSkippingPayment.set(false);

    if (this.isBookingFullyPaid()) {
      this.toast.info("Booking is already fully paid. Proceeding with vehicle handover.");
    } else {
      this.toast.warning("Payment capture skipped. You can proceed with the vehicle handover.");
    }
  }

  ngOnDestroy(): void {
    // Clear all comments when component is destroyed (modal closed)
    this.comments = [];
    this.commentService.updateComments([]);
    this.newCommentPosition = null;
    this.newCommentText = "";
    this.damageInfoOut.clear();

    // Clean up camera resources properly
    this.stopCameraStream();

    if (this.trigger) {
      this.trigger.complete();
    }
    if (this.switchCamera) {
      this.switchCamera.complete();
    }
    // Deactivate camera and close camera modal if it's open
    this.cameraActive = false;
    if (this.cameraModalRef) {
      this.cameraModalRef.dismiss();
      this.cameraModalRef = null;
    }
  }

  initForm() {
    this.vehicleBookingForm = this.fb.group({
      // Payment validation fields
      amountPaid: [0, [Validators.required]],
      paymentMethod: [""],
      paymentReference: [""],
      depositPaid: [this.log.vehicle?.depositAmt || 0, [Validators.required, Validators.min(0)]],
      depositMethod: [""],
      depositReference: [""],

      // Original fields
      fuelOut: [0, [Validators.required, Validators.min(0)]],
      id: [this.log.id],
      mileageOut: ["", [Validators.required, Validators.min(0)]],
      signatureOutHirer: ["", Validators.required], // Customer/hirer signature
      signatureOutHirerName: [""], // Customer/hirer name (auto-populated)
      signatureOutCarRental: ["", Validators.required], // Car rental representative signature
      signatureOutCarRentalName: ["", Validators.required], // Car rental representative name
      damageInfoOut: this.fb.array([]),
      damageImage: [""],
      checkoutItems: [""], // Use a simple FormControl to store a comma-separated list
      newCommentText: [""], // Add this
      acknowledgeTerms: [false, Validators.requiredTrue],

      firstname: [this.log.firstname],
      surname: [this.log.surname],
      email: [this.log.email],
      phone: [this.log.phone],
      vehicleId: [this.log.vehicle?.id],
      vehicle: [this.log.vehicle],
      dropoffTime: [this.formatDateToLocalISOString(new Date(this.log.end || new Date()))],
      pickupTime: [this.formatDateToLocalISOString(new Date(this.log.start || new Date()))],

      discount: [0],
      addons: this.fb.array([]),
      acceptTerms: [false],
    });

    this.vehicleBookingForm.get("email")?.disable();
  }

  formatDateToLocalISOString(date: Date): string {
    const pad = (n: number) => (n < 10 ? "0" + n : n);
    return (
      date.getFullYear() +
      "-" +
      pad(date.getMonth() + 1) +
      "-" +
      pad(date.getDate()) +
      "T" +
      pad(date.getHours()) +
      ":" +
      pad(date.getMinutes())
    );
  }

  ngAfterViewInit(): void {
    // Trigger change detection to ensure @ViewChild references are updated
    this.ngOnInit();

    if (this.imageContainer) {
      console.log("imageContainer initialized:", this.imageContainer.nativeElement);
    } else {
      console.error("imageContainer is not available after change detection.");
    }
  }

  populateVehicleInventories(): void {
    this.vehicleInventories =
      this.log.vehicle.inventory
        .filter((inventory) => !inventory.price)
        .map((inventory) => ({
          ...inventory,
          selected: false,
        })) ?? [];
  }

  toggleInventorySelection(index: number): void {
    const inventory = this.vehicleInventories[index];
    if (inventory) {
      inventory.selected = !inventory.selected;

      // Get all selected inventories
      const selectedInventories = this.vehicleInventories
        .filter((inv) => inv.selected)
        .map((inv) => inv.name) // Map to inventory names
        .join(", ");

      // Update the form control value
      this.vehicleBookingForm.get("checkoutItems")?.setValue(selectedInventories);
    }
  }

  isChecked(itemName: string): boolean {
    const preCheckedItems = ["Baby sitter"]; // Replace with actual pre-checked item list
    return preCheckedItems.includes(itemName);
  }

  updateSliderValueOut(event: Event) {
    const value = parseInt((event.target as HTMLInputElement).value, 10);
    this.vehicleBookingForm.get("fuelOut")?.setValue(value);
  }

  get damageInfoOut(): FormArray {
    return this.vehicleBookingForm.get("damageInfoOut") as FormArray;
  }

  addDamage(): void {
    this.damageInfoOut.push(
      this.fb.group({
        area: ["", Validators.required],
        charge: [0, Validators.required],
        description: ["", Validators.required],
      }),
    );
  }

  removeDamage(index: number): void {
    if (index >= 0 && index < this.comments.length) {
      this.comments.splice(index, 1); // Remove from comments array
      this.damageInfoOut.removeAt(index); // Remove from FormArray
      this.ngOnInit();
      this.toast.success("Damage entry removed successfully.");
    } else {
      this.toast.error("Unable to remove damage entry.");
    }
  }

  // Map comments to form data
  updateDamageInfoOut(): void {
    this.damageInfoOut.clear(); // Clear existing FormArray

    this.comments.forEach((comment) => {
      this.damageInfoOut.push(
        this.fb.group({
          area: [comment.id, Validators.required], // Comment ID as area
          charge: [0, Validators.required],
          description: [comment.text, Validators.required], // Comment text as description
        }),
      );
    });

    this.ngOnInit();
  }

  handleImageClick(event: MouseEvent): void {
    if (!this.imageContainer) {
      console.error("imageContainer is not available.");
      return;
    }

    const containerElement = this.imageContainer.nativeElement;

    const rect = containerElement.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    this.newCommentPosition = { x, y };

    event.stopPropagation();
  }

  addComment(): void {
    if (!this.newCommentPosition || !this.newCommentText.trim()) {
      this.toast.error("Please provide a valid comment and position.");
      return;
    }

    // Create a new comment object
    const newComment = {
      id: this.comments.length + 1, // Generate a unique ID
      x: this.newCommentPosition.x,
      y: this.newCommentPosition.y,
      text: this.newCommentText.trim(), // Assign the text input
      timestamp: new Date(),
    };
    // Add the comment to the comments array
    this.comments.push(newComment);

    // Add the comment to the damageInfoOut FormArray
    const damageInfoOut = this.vehicleBookingForm.get("damageInfoOut") as FormArray;
    damageInfoOut.push(
      this.fb.group({
        area: [newComment.id, Validators.required], // Use comment ID for the area
        charge: [0, Validators.required], // Placeholder for charge
        description: [newComment.text, Validators.required], // Assign the description from newComment.text
      }),
    );
    // Reset the input fields
    this.newCommentText = "";
    this.newCommentPosition = null;

    this.toast.success("Comment added successfully.");
  }

  cancelNewComment() {
    this.newCommentPosition = null;
    this.newCommentText = "";
  }

  selectComment(comment: Comment, event: Event) {
    event.stopPropagation();
    this.selectedComment = comment;
  }

  deleteComment(index: number): void {
    // Remove from comments list
    this.comments.splice(index, 1);

    // Remove from damageInfoOut FormArray
    const damageInfoOut = this.vehicleBookingForm.get("damageInfoOut") as FormArray;
    damageInfoOut.removeAt(index);

    this.toast.success("Comment deleted successfully.");
  }

  dataURLToBlob(dataURL: string): Promise<Blob> {
    return fetch(dataURL)
      .then((res) => res.blob())
      .catch((error) => {
        console.error("Error converting data URL to Blob:", error);
        throw error;
      });
  }

  captureScreenshot(): Promise<void> {
    return new Promise((resolve, reject) => {
      const container = this.imageContainer.nativeElement;

      html2canvas(container)
        .then((canvas) => {
          const base64Image = canvas.toDataURL("image/jpeg"); // Convert to Base64 JPEG

          // Convert the Base64 image to a Blob
          this.dataURLToBlob(base64Image).then((blob) => {
            const file = new File([blob], "annotated-image.jpg", {
              type: "image/jpeg",
            });
            const formData = new FormData();
            formData.append("file", file);

            // Call API to upload the annotated image
            this.bookingService.uploadImage(formData).subscribe({
              next: (response) => {
                if (response && response.fileUrl) {
                  this.vehicleBookingForm.get("damageImage")?.setValue(response.fileUrl); // Save the file URL to the form
                  resolve(); // Resolve the promise
                } else {
                  reject("Failed to retrieve fileUrl");
                }
              },
              error: (error) => {
                reject(error); // Reject the promise
              },
            });
          });
        })
        .catch((error) => {
          reject(error); // Reject the promise
        });
    });
  }

  openSignatureModal(modal: any): void {
    const modalRef = this.modalService.open(modal, {
      size: "lg",
      centered: true,
      windowClass: "signature-modal",
      backdropClass: "signature-modal-backdrop",
    });

    modalRef.result.then(
      (result) => {
        console.log(`Representative signature modal closed with: ${result}`);
      },
      (reason) => {
        console.log(`Representative signature modal dismissed with reason: ${reason}`);
      },
    );

    // Delay to ensure modal content is rendered before initializing the canvas
    setTimeout(() => {
      if (this.signatureCanvasRef) {
        this.initializeCanvas();
      } else {
        console.error("Canvas element not found.");
      }

      // Set focus to the modal dialog for accessibility
      const modalDialog = document.querySelector(".modal-dialog");
      if (modalDialog) {
        (modalDialog as HTMLElement).focus();
      }
    }, 100); // Adjust the delay if needed
  }

  openCustomerSignatureModal(modal: any): void {
    const modalRef = this.modalService.open(modal, {
      size: "lg",
      centered: true,
      windowClass: "signature-modal",
      backdropClass: "signature-modal-backdrop",
    });

    modalRef.result.then(
      (result) => {
        console.log(`Customer signature modal closed with: ${result}`);
      },
      (reason) => {
        console.log(`Customer signature modal dismissed with reason: ${reason}`);
      },
    );

    // Delay to ensure modal content is rendered before initializing the canvas
    setTimeout(() => {
      if (this.signatureCanvasRef) {
        this.initializeCanvas();
      } else {
        console.error("Canvas element not found.");
      }

      // Set focus to the modal dialog for accessibility
      const modalDialog = document.querySelector(".modal-dialog");
      if (modalDialog) {
        (modalDialog as HTMLElement).focus();
      }
    }, 100); // Adjust the delay if needed
  }

  openModal(modal: any): void {
    this.modalService.dismissAll();
    this.modalService.open(modal, {
      size: "lg",
      centered: true,
      windowClass: "edit-booking-modal",
      backdropClass: "edit-booking-modal-backdrop",
    });
  }

  closeModal(): void {
    this.modalService.dismissAll();
  }

  initializeCanvas(): void {
    if (!this.signatureCanvasRef) {
      console.error("Canvas element is not defined.");
      return;
    }

    const canvas = this.signatureCanvasRef.nativeElement;
    this.canvasContext = canvas.getContext("2d");

    if (!this.canvasContext) {
      console.error("Failed to get canvas context.");
      return;
    }

    // Make canvas responsive by setting its dimensions to match its display size
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight || 200; // Fallback height

    // Update canvas dimensions
    canvas.width = displayWidth;
    canvas.height = displayHeight;

    this.canvasContext.fillStyle = "white";
    this.canvasContext.fillRect(0, 0, canvas.width, canvas.height);
    this.canvasContext.lineWidth = 2;
    this.canvasContext.lineCap = "round";
    this.canvasContext.strokeStyle = "black";

    // Mouse events
    canvas.addEventListener("mousedown", this.startDrawing.bind(this));
    canvas.addEventListener("mousemove", this.draw.bind(this));
    canvas.addEventListener("mouseup", this.stopDrawing.bind(this));
    canvas.addEventListener("mouseleave", this.stopDrawing.bind(this));

    // Touch events
    canvas.addEventListener("touchstart", this.startDrawingTouch.bind(this), {
      passive: false,
    });
    canvas.addEventListener("touchmove", this.drawTouch.bind(this), {
      passive: false,
    });
    canvas.addEventListener("touchend", this.stopDrawing.bind(this));
  }

  // Handle touch start event
  startDrawingTouch(event: TouchEvent): void {
    if (!this.canvasContext) return;
    event.preventDefault();
    this.drawing = true;
    const touch = event.touches[0];
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.beginPath();
    this.canvasContext.moveTo(touch.clientX - rect.left, touch.clientY - rect.top);
  }

  // Handle touch move event
  drawTouch(event: TouchEvent): void {
    if (!this.drawing || !this.canvasContext) return;
    event.preventDefault();
    const touch = event.touches[0];
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.lineTo(touch.clientX - rect.left, touch.clientY - rect.top);
    this.canvasContext.strokeStyle = "black";
    this.canvasContext.lineWidth = 2;
    this.canvasContext.stroke();
  }

  startDrawing(event: MouseEvent): void {
    if (!this.canvasContext) return;
    this.drawing = true;
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.beginPath();
    this.canvasContext.moveTo(event.clientX - rect.left, event.clientY - rect.top);
  }

  draw(event: MouseEvent): void {
    if (!this.drawing || !this.canvasContext) return;
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.lineTo(event.clientX - rect.left, event.clientY - rect.top);
    this.canvasContext.strokeStyle = "black";
    this.canvasContext.lineWidth = 2;
    this.canvasContext.stroke();
  }

  stopDrawing(): void {
    this.drawing = false;
  }

  clearSignature(): void {
    if (!this.canvasContext) return;
    this.canvasContext.clearRect(
      0,
      0,
      this.signatureCanvasRef.nativeElement.width,
      this.signatureCanvasRef.nativeElement.height,
    );
  }

  saveSignature(modal: any): void {
    if (!this.signatureCanvasRef) {
      this.toast.error("Signature canvas is not initialized.");
      return;
    }

    const canvas = this.signatureCanvasRef.nativeElement;

    // Convert canvas to Blob as JPG
    canvas.toBlob((blob) => {
      if (!blob) {
        this.toast.error("Failed to generate signature image.");
        return;
      }

      const file = new File([blob], "signature.jpg", { type: "image/jpeg" });
      const formData = new FormData();
      formData.append("file", file);

      // Call API to upload the signature
      this.bookingService.uploadSignature(formData).subscribe({
        next: (response) => {
          if (response && response.fileUrl) {
            this.savedSignature = response.fileUrl; // Update savedSignature with the uploaded file URL
            this.vehicleBookingForm.get("signatureOutCarRental")?.setValue(response.fileUrl);
            this.toast.success("Representative signature saved and uploaded successfully!");
          } else {
            this.toast.error("Failed to retrieve the signature URL.");
          }
          modal.close();
        },
        error: (error) => {
          console.error("Error uploading signature:", error);
          this.toast.error("Failed to upload the signature.");
        },
      });
    }, "image/jpeg");
  }

  saveCustomerSignature(modal: any): void {
    if (!this.signatureCanvasRef) {
      this.toast.error("Signature canvas is not initialized.");
      return;
    }

    const canvas = this.signatureCanvasRef.nativeElement;

    // Convert canvas to Blob as JPG
    canvas.toBlob((blob) => {
      if (!blob) {
        this.toast.error("Failed to generate signature image.");
        return;
      }

      const file = new File([blob], "customer-signature.jpg", { type: "image/jpeg" });
      const formData = new FormData();
      formData.append("file", file);

      // Call API to upload the customer signature
      this.bookingService.uploadSignature(formData).subscribe({
        next: (response) => {
          if (response && response.fileUrl) {
            this.savedCustomerSignature = response.fileUrl; // Update savedCustomerSignature with the uploaded file URL
            this.vehicleBookingForm.get("signatureOutHirer")?.setValue(response.fileUrl);
            this.toast.success("Customer signature saved and uploaded successfully!");
          } else {
            this.toast.error("Failed to retrieve the customer signature URL.");
          }
          modal.close();
        },
        error: (error) => {
          console.error("Error uploading customer signature:", error);
          this.toast.error("Failed to upload the customer signature.");
        },
      });
    }, "image/jpeg");
  }

  private async waitForUIUpdate(): Promise<void> {
    this.cdr.detectChanges();
    await new Promise((resolve) => setTimeout(resolve, 0));
  }

  isSubmitting = signal(false);

  async handleSubmit(): Promise<void> {
    if (this.isSubmitting()) return; // Prevent double submission

    // If there's an open comment box, close it
    if (this.newCommentPosition) {
      this.cancelNewComment();
      await this.waitForUIUpdate();
    }

    this.hasSubmitted.set(true);
    if (this.vehicleBookingForm.invalid) {
      const invalidFields = Object.keys(this.vehicleBookingForm.controls).filter((field) => {
        const control = this.vehicleBookingForm.get(field);
        return control && control.invalid;
      });

      this.toast.error("Please fill out all required fields." + invalidFields);
      return;
    }

    // Check if payment validation has been completed
    if (!this.paymentValidated()) {
      this.toast.error(
        "Please validate payments and deposits before proceeding with the handover.",
      );
      return;
    }

    this.isSubmitting.set(true); // Set submitting state to true

    try {
      const selectedInventories = this.vehicleInventories
        .filter((item: any) => item.selected)
        .map((item: any) => item.name)
        .join(", ");

      this.vehicleBookingForm.get("checkoutItems")?.setValue(selectedInventories);

      await this.captureScreenshot();

      const {
        // Vehicle condition information only - payment already processed
        fuelOut,
        id,
        mileageOut,
        damageImage,
        signatureOutHirer,
        signatureOutHirerName,
        signatureOutCarRental,
        signatureOutCarRentalName,
        damageInfoOut,
      } = this.vehicleBookingForm.value;

      const updatedDamageInfoOut = (damageInfoOut || []).map((item: any) => ({
        ...item,
        charge: item.charge || 0,
      }));

      const payload = {
        // Original handover information
        fuelOut,
        id,
        damageImage,
        mileageOut,
        signatureOutHirer,
        signatureOutHirerName,
        signatureOutCarRental,
        signatureOutCarRentalName,
        checkoutItems: this.vehicleBookingForm.get("checkoutItems")?.value || "",
        damageInfoOut: updatedDamageInfoOut,
      };

      this.bookingService.handoverVehicle(payload).subscribe({
        next: () => {
          this.toast.success("Vehicle handover details submitted successfully!");
          this.modalService.dismissAll();
          this.resetForm();
          this.updated.emit(true);
        },
        error: () => {
          this.toast.error("An error occurred while submitting the form.");
        },
        complete: () => {
          this.isSubmitting.set(false); // Reset submitting state
        },
      });
      this.resetForm();
    } catch (error) {
      this.isSubmitting.set(false); // Reset submitting state on error
      this.toast.error("An error occurred during the image upload or form submission.");
    }
  }

  resetForm(): void {
    this.vehicleBookingForm.reset(); // Reset the main form
    this.damageInfoOut.clear(); // Clear the dynamic FormArray
    this.savedSignature = null; // Clear the saved representative signature
    this.savedCustomerSignature = null; // Clear the saved customer signature
    this.comments = []; // Clear the comments list
    this.newCommentText = "";
    this.newCommentPosition = null;

    // Reset payment validation state
    this.paymentValidated.set(false);
    this.isValidatingPayment.set(false);

    // ✅ Notify commentService to clear stored comments
    this.commentService.updateComments([]);

    // ✅ Force UI update
    this.cdr.detectChanges();

    // Reset document URLs
    this.selectedLicenseUrl = null;
    this.selectedResidenceUrl = null;
  }

  async downloadPDF() {
    this.isGeneratingPDF.set(true);
    try {
      await this.pdfService.generatePDF("handover-form", "handover-form");
      this.toast.success("PDF generated successfully. Check your downloads");
    } catch (error) {
      this.toast.error("Failed to generate PDF");
    } finally {
      this.isGeneratingPDF.set(false);
    }
  }

  viewHirerDetails(): void {
    // Reset the flag when opening the modal
    this.documentUploaded = false;

    const modalRef = this.modalService.open(this.hirerDetailsModal, {
      centered: true,
      size: "lg",
      windowClass: "hirer-details-modal",
      backdropClass: "hirer-details-modal-backdrop",
      animation: true,
    });

    // Combined handling for both close and dismiss
    const updateClientIfNeeded = async () => {
      if (this.documentUploaded) {
        try {
          const updatedClient = await lastValueFrom(
            this.clientService.getClientById(this.log.client.id),
          );

          if (updatedClient) {
            this.log.client = { ...updatedClient };
            this.cdr.markForCheck();
            this.cdr.detectChanges();
          }
        } catch (error) {
          this.toast.error("Failed to refresh client data");
          console.error("Error fetching updated client:", error);
        }
      }
    };

    // Subscribe to both events with the same handler
    modalRef.closed.subscribe(() => updateClientIfNeeded());
    modalRef.dismissed.subscribe(() => updateClientIfNeeded());
  }

  // Add ViewChild decorator to get reference to the modal template
  @ViewChild("hirerDetailsModal") hirerDetailsModal: any;

  selectedLicenseFile: File | null = null;
  selectedResidenceFile: File | null = null;

  // Add these properties to store URLs
  selectedLicenseUrl: string | null = null;
  selectedResidenceUrl: string | null = null;

  @ViewChild("licenseInput") licenseInput!: ElementRef;
  @ViewChild("residenceInput") residenceInput!: ElementRef;

  // Camera modal properties
  @ViewChild("cameraModal") cameraModal: any;
  @ViewChild(WebcamComponent) webcamComponent?: WebcamComponent;
  showCameraModal = false;
  cameraActive = false; // New property to control camera stream
  cameraInitialized = false; // Track if camera has been initialized
  currentDocumentType: "LICENCE" | "PROOF_OF_RESIDENCE" | null = null;
  private cameraModalRef: any = null; // Store reference to opened camera modal

  // Camera functionality
  private trigger: Subject<void> = new Subject<void>();
  private switchCamera: Subject<boolean | string> = new Subject<boolean | string>();

  webcamImage: WebcamImage | null = null;
  capturedImageDataUrl: string | null = null;

  get triggerObservable(): Observable<void> {
    return this.trigger.asObservable();
  }

  get switchCameraObservable(): Observable<boolean | string> {
    return this.switchCamera.asObservable();
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDrop(event: DragEvent, documentType: "LICENCE" | "PROOF_OF_RESIDENCE"): void {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFile(files[0], documentType);
    }
  }

  onFileChange(event: any, documentType: "LICENCE" | "PROOF_OF_RESIDENCE"): void {
    const file = event.target.files[0];
    if (file) {
      this.handleFile(file, documentType);
    }
  }

  private validateFile(file: File): boolean {
    const validTypes = ["image/png", "image/jpeg", "image/jpg", "image/webp"];
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes

    if (!validTypes.includes(file.type)) {
      this.toast.error("Please select a valid image file (PNG, JPG, JPEG, or WEBP)");
      return false;
    }

    if (file.size > maxSize) {
      this.toast.error("Image size must not exceed 10MB");
      return false;
    }

    return true;
  }

  private handleFile(file: File, documentType: "LICENCE" | "PROOF_OF_RESIDENCE"): void {
    if (!this.validateFile(file)) {
      return;
    }

    if (documentType === "LICENCE") {
      this.selectedLicenseFile = file;
      this.uploadLicense(file);
    } else {
      this.selectedResidenceFile = file;
      this.uploadProofOfResidence(file);
    }
  }

  // Add this property to track document uploads
  private documentUploaded = false;

  async uploadClientDocument(
    file: File,
    documentType: "DRIVER" | "PROOF_RESIDENCE",
  ): Promise<void> {
    try {
      let fileToUpload = file;

      if (file.type.startsWith("image/")) {
        fileToUpload = await this.compressImage(file, 1024, 1024, 0.7);
      }

      const formData = new FormData();
      formData.append("file", fileToUpload);

      const uploadResponse = await lastValueFrom(this.clientService.uploadClientDocument(formData));

      // Store the URL based on document type
      if (documentType === "DRIVER") {
        this.selectedLicenseUrl = uploadResponse.fileUrl;
      } else {
        this.selectedResidenceUrl = uploadResponse.fileUrl;
      }

      const documentData = {
        id: this.log.client.id,
        clientDocs: [
          {
            url: uploadResponse.fileUrl,
            name: documentType,
          },
        ],
      };

      await lastValueFrom(this.clientService.addClientDocument(documentData));

      // Set the flag when document is uploaded successfully
      this.documentUploaded = true;
      this.toast.success("Document uploaded successfully");
    } catch (error) {
      this.toast.error("Failed to upload document");
      console.error("License upload error:", error);
    }
  }

  async uploadProofOfResidence(file: File): Promise<void> {
    await this.uploadClientDocument(file, "PROOF_RESIDENCE");
  }

  async uploadLicense(file: File): Promise<void> {
    await this.uploadClientDocument(file, "DRIVER");
  }

  compressImage(file: File, maxWidth: number, maxHeight: number, quality: number): Promise<File> {
    return new Promise((resolve) => {
      const img = new Image();
      const reader = new FileReader();

      reader.onload = (event: any) => {
        img.src = event.target.result;
      };

      img.onload = () => {
        let width = img.width;
        let height = img.height;

        if (width > maxWidth || height > maxHeight) {
          if (width > height) {
            height = Math.floor((maxHeight / width) * height);
            width = maxWidth;
          } else {
            width = Math.floor((maxWidth / height) * width);
            height = maxHeight;
          }
        }

        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d")!;
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(new File([blob], file.name, { type: "image/jpeg" }));
            } else {
              resolve(file);
            }
          },
          "image/jpeg",
          quality,
        );
      };

      reader.readAsDataURL(file);
    });
  }

  viewUploadedDocument(url: string, documentType: string): void {
    const titles = {
      DRIVER: "Driver's License",
      PROOF_RESIDENCE: "Proof of Residence",
    };

    const modalRef = this.modalService.open(DocumentViewerModalComponent, {
      centered: true,
      size: "md",
      windowClass: "document-viewer-modal",
      backdropClass: "document-viewer-modal-backdrop",
      animation: true,
    });

    modalRef.componentInstance.documentUrl = url;
    modalRef.componentInstance.title = titles[documentType] || "Document";
  }

  getDocumentByType(docType: string) {
    return this.log.client.clientDocs?.find((doc) => doc.name === docType);
  }

  formatAddress(address: any): string | null {
    if (!address) return null;

    const addressParts = [address.firstLine, address.town, address.county].filter(Boolean);

    return addressParts.length > 0 ? addressParts.join(", ") : null;
  }

  openEditAddressModal(): void {
    // Populate form with existing address data
    if (this.log.client?.address) {
      this.addressForm.patchValue({
        firstLine: this.log.client.address.firstLine || "",
        town: this.log.client.address.town || "",
        county: this.log.client.address.county || "",
      });
    }

    const modalRef = this.modalService.open(this.editAddressModal, {
      centered: true,
      size: "md",
      windowClass: "edit-address-modal",
      backdropClass: "edit-address-modal-backdrop",
      animation: true,
    });

    // Store the modal reference for later use
    this.activeEditAddressModal = modalRef;
  }

  async updateAddress(): Promise<void> {
    if (this.addressForm.invalid) {
      Object.keys(this.addressForm.controls).forEach((key) => {
        const control = this.addressForm.get(key);
        if (control?.invalid) {
          control.markAsTouched();
        }
      });
      return;
    }

    try {
      this.isUpdatingAddress.set(true);
      const formValue = this.addressForm.value;

      const updateData = {
        id: this.log.client?.id,
        address: {
          firstLine: formValue.firstLine,
          secondLine: "",
          town: formValue.town,
          county: formValue.county,
          postcode: "",
          country: "",
        },
      };

      await lastValueFrom(this.clientService.updateClient(updateData));

      // Update the local client data
      if (this.log.client) {
        this.log.client.address = updateData.address;
      }

      // Close only the edit address modal
      if (this.activeEditAddressModal) {
        this.activeEditAddressModal.close();
      }

      this.toast.success("Address updated successfully");
      this.cdr.detectChanges();
    } catch (error) {
      this.toast.error("Failed to update address");
      console.error("Error updating address:", error);
    } finally {
      this.isUpdatingAddress.set(false);
    }
  }

  calculateDaysHired(): number {
    if (this.log.invoices && this.log.invoices.length > 0) {
      const rentalItems = this.log.invoices[0].invoiceItemResult.filter((item) =>
        item.description.includes("Car rental for"),
      );
      if (rentalItems.length > 0) {
        return rentalItems.length;
      }
    }

    // Default fallback
    return 1;
  }

  getTotalMileageAllowance(): number {
    if (this.log.vehicle.maxDailyMileage === 0) {
      return 0;
    }

    const baseMileage = this.log.vehicle.maxDailyMileage;
    const extraMileage =
      this.log.promotion?.promotionType === "EXTRA_MILEAGE" ? this.log.promotion.extraMileage : 0;

    return baseMileage + extraMileage;
  }

  getReturnMileageLimit() {
    const daysHired = this.calculateDaysHired();
    const totalMileageAllowance = this.getTotalMileageAllowance();

    // Determine starting mileage based on booking state
    let startingMileage: number;
    if (this.log.status === "BOOKED") {
      startingMileage = this.log.vehicle.mileage;
    } else {
      startingMileage = this.log.mileageOut ?? 0;
    }

    const totalAllowedMileage = daysHired * totalMileageAllowance;

    return totalAllowedMileage === 0 ? 0 : startingMileage + totalAllowedMileage;
  }

  // Camera functionality methods
  openCameraModal(documentType: "LICENCE" | "PROOF_OF_RESIDENCE"): void {
    this.currentDocumentType = documentType;
    this.showCameraModal = true;
    this.capturedImageDataUrl = null;
    this.webcamImage = null;
    this.cameraInitialized = false;

    // Use setTimeout to ensure the modal is rendered before opening
    setTimeout(() => {
      this.cameraModalRef = this.modalService.open(this.cameraModal, {
        size: "lg",
        centered: true,
        backdrop: "static",
        keyboard: false,
        windowClass: "camera-modal",
        backdropClass: "camera-modal-backdrop",
      });

      // Handle modal close events to ensure camera cleanup
      this.cameraModalRef.result.catch(() => {
        // Modal was dismissed
        this.stopCameraStream();
        this.cameraActive = false;
        this.cameraInitialized = false;
      });

      // Activate camera only after modal is opened
      setTimeout(() => {
        this.cameraActive = true;
        this.cdr.detectChanges();
      }, 100);
    });
  }

  captureImage(): void {
    this.trigger.next();
  }

  switchCameraDevice(): void {
    this.switchCamera.next(true);
  }

  onImageCaptured(webcamImage: WebcamImage): void {
    this.webcamImage = webcamImage;
    this.capturedImageDataUrl = webcamImage.imageAsDataUrl;
    console.log("Image captured:", webcamImage);
  }

  onCameraSuccess(): void {
    this.cameraInitialized = true;
    console.log("Camera initialized successfully");
  }

  onCameraInitError(error: WebcamInitError): void {
    console.error("Camera init error:", error);
    this.cameraInitialized = false;

    if (error.mediaStreamError && error.mediaStreamError.name === "NotAllowedError") {
      this.toast.error("Camera access was denied. Please allow camera access and try again.");
    } else if (error.mediaStreamError && error.mediaStreamError.name === "NotFoundError") {
      this.toast.error("No camera found. Please ensure a camera is connected.");
    } else {
      this.toast.error("Failed to initialize camera. Please try again.");
    }

    // Ensure camera resources are cleaned up on error
    this.closeCameraModal();
  }

  closeCameraModal(): void {
    // Stop camera stream before deactivating
    this.stopCameraStream();

    // Deactivate camera first to stop the stream
    this.cameraActive = false;
    this.cameraInitialized = false;
    this.showCameraModal = false;
    this.currentDocumentType = null;
    this.capturedImageDataUrl = null;
    this.webcamImage = null;

    // Force change detection to ensure camera component is destroyed
    this.cdr.detectChanges();

    // Close only the camera modal if it exists
    if (this.cameraModalRef) {
      this.cameraModalRef.dismiss();
      this.cameraModalRef = null;
    }
  }

  /**
   * Properly stops all camera streams to ensure the camera LED turns off
   * This is essential for privacy and to prevent resource leaks
   */
  private stopCameraStream(): void {
    try {
      // Method 1: Try to access the webcam component directly and stop its streams
      if (this.webcamComponent) {
        // Access the internal media stream if possible
        const component = this.webcamComponent as any;
        if (component.mediaStream) {
          component.mediaStream.getTracks().forEach((track: MediaStreamTrack) => {
            track.stop();
            console.log("Stopped media track:", track.kind);
          });
        }
      }

      // Method 2: Stop all active media streams globally (backup method)
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        // This is a more aggressive approach to ensure all streams are stopped
        navigator.mediaDevices
          .enumerateDevices()
          .then((devices) => {
            // We can't directly stop all streams, but we can ensure our component's stream is stopped
            console.log("Camera cleanup completed");
          })
          .catch((error) => {
            console.warn("Error during camera cleanup:", error);
          });
      }
    } catch (error) {
      console.warn("Error while stopping camera stream:", error);
    }
  }

  async confirmCapturedImage(): Promise<void> {
    if (!this.webcamImage || !this.currentDocumentType) {
      this.toast.error("No image captured or document type selected");
      return;
    }

    try {
      // Convert data URL to File
      const response = await fetch(this.webcamImage.imageAsDataUrl);
      const blob = await response.blob();
      const timestamp = new Date().getTime();
      const fileName = `camera_capture_${this.currentDocumentType}_${timestamp}.jpg`;
      const file = new File([blob], fileName, { type: "image/jpeg" });

      // Handle the file the same way as regular file upload
      this.handleFile(file, this.currentDocumentType);

      this.closeCameraModal();
      this.toast.success("Image captured and uploaded successfully");
    } catch (error) {
      console.error("Error processing captured image:", error);
      this.toast.error("Failed to process captured image");
    }
  }
}
