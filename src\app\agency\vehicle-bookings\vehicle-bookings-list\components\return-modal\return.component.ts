import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  OnDestroy,
  Output,
  signal,
  ViewChild,
} from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { NgbModal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import { ToastrService } from "ngx-toastr";
import { VehicleBookingService } from "src/app/shared/services/vehicle-booking.service";
import { Comment } from "src/app/shared/services/types";
import { CommentService } from "src/app/shared/services/comment.service";
import html2canvas from "html2canvas";
import { PDFService } from "src/app/shared/services/pdf.service";
import { firstValueFrom } from "rxjs";
import { CurrencyResolutionService } from "src/app/shared/services/currency-resolution.service";
import { CurrencyService } from "src/app/shared/services/currency.service";

interface RatingItem {
  status: string;
  title: string;
  subtitle: string;
  rate: number;
}

@Component({
  selector: "return-modal",
  templateUrl: "./return.component.html",
  styleUrls: ["./return.component.css"],
  standalone: false,
})
export class ReturnComponent implements OnInit, OnDestroy, OnChanges {
  @Input() log: any = {}; // Booking log details
  @Output() updated = new EventEmitter<boolean>();
  @ViewChild("signatureCanvas")
  signatureCanvasRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild("hirerRatingModal")
  hirerRatingModal: any;
  @ViewChild("imageContainer", { static: false }) imageContainer!: ElementRef;
  @Input() checkoutItems: string = ""; // Input from backend
  previewImages: string[] = [];
  popupImage: string | null = null;

  hasSubmitted = signal(false);
  isGeneratingPDF = signal(false);
  isSubmitting = signal(false);
  isSubmittingRating = signal(false);
  mileageError: string = "";
  returnForm!: FormGroup;
  checkoutForm!: FormGroup;
  comments: Comment[] = [];
  newCommentPosition: { x: number; y: number } | null = null;
  newCommentText: string = "";
  selectedComment: Comment | null = null;
  savedSignature: string | null = null;
  savedCustomerSignature: string | null = null;
  private canvasContext: CanvasRenderingContext2D | null = null;
  private drawing = false;
  sliderValueIn: number;
  submitted: boolean;
  vehicleInventories: any;
  isSliderDisabled = false; // Default state: editable
  today: Date = new Date(); // Add today property
  acknowledgeTerms: boolean = false;

  // Editable name state
  hirerName: string = "";
  signatureInName: string = "";

  damageImages: any[] = []; // Stores images
  uploadUrls: string[] = []; // Stores uploaded image URLs

  ratingItems: RatingItem[] = [
    {
      status: "TIMELINESS",
      title: "Timeliness",
      subtitle: "How punctual was the hirer in returning the vehicle?",
      rate: 0,
    },
    {
      status: "CONDITION",
      title: "Vehicle Condition",
      subtitle: "How good was the condition of the vehicle upon return?",
      rate: 0,
    },
    {
      status: "CLEANLINESS",
      title: "Vehicle Cleanliness",
      subtitle: "How clean was the vehicle upon return?",
      rate: 0,
    },
    {
      status: "PROFESSIONALISM",
      title: "Professionalism",
      subtitle: "How would you rate the hirer's professionalism?",
      rate: 0,
    },
    {
      status: "REPEAT",
      title: "Repeat Business",
      subtitle: "How likely are you to rent to this hirer again?",
      rate: 0,
    },
  ];

  ratingComment: string = "";

  constructor(
    private fb: FormBuilder,
    private toast: ToastrService,
    private modalService: NgbModal,
    private bookingService: VehicleBookingService,
    private commentService: CommentService,
    private cdr: ChangeDetectorRef,
    private pdfService: PDFService,
    private currencyResolutionService: CurrencyResolutionService,
    private currencyService: CurrencyService,
  ) {
    this.returnForm = this.fb.group({
      fuelIn: [0, [Validators.required, Validators.min(0)]],
      id: [null, Validators.required],
      mileageIn: ["", [Validators.required, Validators.min(0)]],
      signatureInCarRental: [null, Validators.required], // Car rental representative signature
      signatureInCarRentalName: ["", Validators.required], // Car rental representative name
      signatureInHirer: [null, Validators.required], // Customer/hirer signature
      signatureInHirerName: [""], // Customer/hirer name (auto-populated)
      damageInfoIn: this.fb.array([]),
      damageImageIn: [""],
      items: this.fb.array([]), // FormArray for checkout items
      extraCharges: this.fb.array([]), // Stores manually added extraCharges
      newCommentText: [""],
      acknowledgeTerms: [false, Validators.requiredTrue],
    });

    this.commentService.comments$.subscribe((comments) => {
      this.comments = comments;
      this.updateDamageInfoIn(); // Update form whenever comments change
    });
  }

  getHeaderCurrency(formatted = true) {
    return this.currencyResolutionService.getHeaderCurrency(formatted, this.log);
  }

  ngOnInit(): void {
    this.patchCheckoutItems();

    if (this.log && this.log.status === "WAITINGAUTH") {
      this.isSliderDisabled = true;
    }

    this.vehicleInventories = this.log.vehicle.inventory.map((inventory) => ({
      ...inventory,
      selected: this.isChecked(inventory.name),
    }));

    // Change the validation for mileage in
    this.returnForm
      .get("mileageIn")
      ?.setValidators([Validators.required, Validators.min(this.log.mileageOut)]);

    // Subscribe to mileage in changes
    this.returnForm.get("mileageIn")?.valueChanges.subscribe((value) => {
      if (this.returnForm.get("mileageIn")?.valid && value) {
        this.updateExcessMileageCharge();
      }
    });
  }

  ngOnDestroy(): void {
    // Clear all comments when component is destroyed (modal closed)
    this.comments = [];
    this.commentService.updateComments([]);
    this.newCommentPosition = null;
    this.newCommentText = "";

    // If you have a FormArray for damage info like in the handover component
    if (this.extraCharges) {
      this.extraCharges.clear();
    }
  }

  openPopup(image: string): void {
    this.popupImage = image;
  }

  closePopup(): void {
    this.popupImage = null;
  }

  private updateExcessMileageCharge(): void {
    const mileageResult = this.calculateMileageCharges();

    // Remove any existing excess mileage charge
    this.extraCharges.controls = this.extraCharges.controls.filter(
      (control) => !control.get("description")?.value.includes("Excess Mileage Cost"),
    );

    // Only add new charge if there is an excess
    if (mileageResult.excessCharge > 0) {
      const currency = this.getHeaderCurrency(false);
      const formattedRate = this.currencyService.formatCurrencyIntl(
        mileageResult.excessMileageRate,
        currency,
      );

      this.extraCharges.push(
        this.fb.group({
          area: ["Mileage"],
          description: [
            `Excess Mileage Cost (${mileageResult.excessMileage} km @ ${formattedRate}/km)`,
          ],
          charge: [mileageResult.excessCharge, [Validators.required, Validators.min(0)]],
          type: ["EXTRA"],
        }),
      );
    }

    // Force change detection
    this.cdr.detectChanges();
  }

  ngOnChanges(): void {
    if (this.log?.id) {
      console.log("Patching log data to the form:", this.log);
      this.returnForm.patchValue({
        id: this.log.id,
        fuelIn: this.log.fuelIn || this.returnForm.value.fuelIn,
        mileageIn: this.log.mileageIn || this.returnForm.value.mileageIn,
        signatureInCarRental:
          this.log.signatureInCarRental || this.returnForm.value.signatureInCarRental,
        signatureInCarRentalName:
          this.log.signatureInCarRentalName || this.returnForm.value.signatureInCarRentalName,
        signatureInHirerName: `${this.log.firstname} ${this.log.surname}`,
        damageImageIn: this.log.damageImageIn || this.returnForm.value.damageImageIn,
      });

      // Only set signatureInHirer if we don't already have a newly captured one
      if (!this.savedCustomerSignature) {
        this.returnForm.patchValue({
          signatureInHirer: this.log.signatureInHirer || this.returnForm.value.signatureInHirer,
        });
        // Update the display property if there's an existing signature
        if (this.log.signatureInHirer) {
          this.savedCustomerSignature = this.log.signatureInHirer;
        }
      }

      // Update the representative signature display if there's an existing one
      if (this.log.signatureInCarRental && !this.savedSignature) {
        this.savedSignature = this.log.signatureInCarRental;
      }

      // Preserve comments or damage info
      if (this.log.damageInfoIn?.length) {
        this.damageInfoIn.clear();
        this.log.damageInfoIn.forEach((damage: any) => {
          this.damageInfoIn.push(
            this.fb.group({
              area: [damage.area, Validators.required],
              charge: [damage.charge, Validators.required],
              description: [damage.description, Validators.required],
            }),
          );
        });
      }

      this.cdr.detectChanges(); // Ensure UI updates after patching
    }
  }

  // Add this helper function to get total mileage allowance per day
  getTotalMileageAllowance(): number {
    if (this.log.vehicle.maxDailyMileage === 0) {
      return 0; // Unlimited
    }

    const baseMileage = this.log.vehicle.maxDailyMileage;
    const extraMileage =
      this.log.promotion?.promotionType === "EXTRA_MILEAGE" ? this.log.promotion.extraMileage : 0;

    return baseMileage + extraMileage;
  }

  calculateDaysHired(): number {
    if (this.log.invoices && this.log.invoices.length > 0) {
      const rentalItems = this.log.invoices[0].invoiceItemResult.filter((item) =>
        item.description.includes("Car rental for"),
      );
      if (rentalItems.length > 0) {
        return rentalItems.length;
      }
    }

    // Default fallback
    return 1;
  }

  calculateMileageCharges() {
    const totalDailyAllowance = this.getTotalMileageAllowance();
    const excessMileageRate = this.log.vehicle.excessMileageRate;

    // Create result object with default values
    const result = {
      excessCharge: 0,
      excessMileage: 0,
      excessMileageRate: excessMileageRate,
    };

    // If unlimited mileage (0 means unlimited)
    if (totalDailyAllowance === 0) {
      return result;
    }

    const daysHired = this.calculateDaysHired();
    const mileageUsed = this.returnForm.get("mileageIn").value - this.log.mileageOut;
    const totalMileageAllowed = totalDailyAllowance * daysHired;
    const excessMileage = Math.max(0, mileageUsed - totalMileageAllowed);

    // No excess mileage charge if within allowance
    if (excessMileage <= 0) {
      return result;
    }

    // Calculate excess charge
    const excessMileageCharge = excessMileage * excessMileageRate;

    // Update and return result object
    result.excessCharge = excessMileageCharge;
    result.excessMileage = excessMileage;

    return result;
  }

  async onFileSelected(event: any): Promise<void> {
    const files: File[] = event.target.files;
    if (files) {
      const fileProcessingPromises = Array.from(files).map(async (file) => {
        let processedFile = file;
        if (file.size > 1024 * 1024) {
          // If file > 1MB, compress it
          processedFile = await this.compressImage(file, 1024, 1024, 0.7); // Resize to 1024x1024, 70% quality
        }

        return new Promise<void>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e: any) => {
            this.damageImages.push({
              file: processedFile,
              previewUrl: e.target.result,
            });
            resolve();
          };
          reader.readAsDataURL(processedFile);
        });
      });

      // Wait for all files to be processed
      await Promise.all(fileProcessingPromises);

      // Now submit the images after all files are processed
      await this.submitDamageImages();
    }
  }

  // Compress image using canvas
  compressImage(file: File, maxWidth: number, maxHeight: number, quality: number): Promise<File> {
    return new Promise((resolve) => {
      const img = new Image();
      const reader = new FileReader();

      reader.onload = (event: any) => {
        img.src = event.target.result;
      };

      img.onload = () => {
        let width = img.width;
        let height = img.height;

        if (width > maxWidth || height > maxHeight) {
          if (width > height) {
            height = Math.floor((maxHeight / width) * height);
            width = maxWidth;
          } else {
            width = Math.floor((maxWidth / height) * width);
            height = maxHeight;
          }
        }

        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d")!;
        ctx.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(new File([blob], file.name, { type: "image/jpeg" }));
            } else {
              resolve(file);
            }
          },
          "image/jpeg",
          quality,
        );
      };

      reader.readAsDataURL(file);
    });
  }

  // Remove an image from the list
  removeImage(index: number): void {
    this.damageImages.splice(index, 1);
  }

  // Submit compressed images to backend
  async submitDamageImages(): Promise<void> {
    if (this.damageImages.length === 0) {
      this.toast.success("No damage images to upload");
      return;
    }

    try {
      const promises = [];
      this.damageImages.forEach((image) => {
        const formData = new FormData();
        formData.append("file", image.file);
        const response = firstValueFrom(this.bookingService.uploadDamagedImages(formData));
        promises.push(response);
      });
      const images = (await Promise.all(promises)).map((image) => image.fileUrl);
      this.uploadUrls = images;
      this.toast.success("Damage images successfuly saved");
    } catch (error) {
      console.error("Upload failed:", error);
      this.toast.error("Failed to upload damage images.");
    }
  }

  get items(): FormArray {
    return this.returnForm.get("items") as FormArray;
  }

  get extraCharges(): FormArray {
    return this.returnForm.get("extraCharges") as FormArray;
  }

  addExtra(): void {
    this.extraCharges.push(
      this.fb.group({
        area: [""], // Allow custom input for extra area
        description: ["", Validators.required], // Custom description
        charge: [0, [Validators.required, Validators.min(0)]], // Charge input
        type: ["EXTRA"],
      }),
    );
  }

  // ✅ Remove extraCharges without affecting Damage Info
  removeExtra(index: number): void {
    this.extraCharges.removeAt(index);
  }

  patchCheckoutItems(): void {
    if (this.log && this.log.checkoutItems) {
      // Clear any existing items
      this.items.clear();

      // Split the checkoutItems string from the backend and map to FormArray
      const checkoutItemsArray = this.log.checkoutItems
        .split(",")
        .map((item: string) => item.trim());
      checkoutItemsArray.forEach((item: string) => {
        this.items.push(
          this.fb.group({
            name: [item], // Item name from backend
            systemChecked: [true], // Pre-checked (disabled)
            userChecked: [false], // User confirmation checkbox
          }),
        );
      });

      this.cdr.detectChanges(); // Ensure UI updates
    }
  }

  // savesignatureInName(): void {
  //   const signatureInName = this.returnForm.get('signatureInName')?.value;

  //   if (!signatureInName || signatureInName.trim() === '') {
  //     this.toast.error('Hirer name cannot be empty!');
  //     return;
  //   }
  //   this.toast.success('Hirer name saved successfully!');
  // }

  isChecked(itemName: string): boolean {
    const preCheckedItems = ["Baby sitter"]; // Replace with actual pre-checked item list
    return preCheckedItems.includes(itemName);
  }

  // Slider
  updateSliderValueIn(event: Event) {
    const value = parseInt((event.target as HTMLInputElement).value, 10);
    this.returnForm.get("fuelIn")?.setValue(value);
    console.log("Fuel Level Out Updated:", value);
  }

  //Vehicle Image

  get damageInfoIn(): FormArray {
    return this.returnForm.get("damageInfoIn") as FormArray;
  }

  private setInitialFormData(): void {
    const initialData = {
      fuelIn: 8,
      id: 12,
      mileageIn: 433,
      signatureIn: "",
      signatureInName: "",
      damageImageIn: "",
      damageImageUploaded: "",
      damageInfoIn: [
        {
          area: "1",
          charge: 50, // Add a charge for the damage
          description: "It is so big",
        },
      ],
      extraCharges: [
        {
          area: "1",
          charge: 50, // Add a charge for the damage
          description: "It is so big",
          type: "EXTRA",
        },
      ],
    };

    this.returnForm.patchValue({
      fuelIn: initialData.fuelIn,
      id: this.log.id,
      mileageIn: initialData.mileageIn,
      signatureIn: initialData.signatureIn,
      signatureInName: initialData.signatureInName,
      damageImageIn: initialData.damageImageIn,
    });

    initialData.damageInfoIn.forEach((damage) =>
      this.damageInfoIn.push(
        this.fb.group({
          area: [damage.area, Validators.required],
          charge: [damage.charge, [Validators.required, Validators.min(0)]],
          description: [damage.description, Validators.required],
        }),
      ),
    );

    initialData.extraCharges.forEach((extra) =>
      this.damageInfoIn.push(
        this.fb.group({
          area: [extra.area, Validators.required],
          charge: [extra.charge, [Validators.required, Validators.min(0)]],
          description: [extra.description, Validators.required],
          type: [extra.type, Validators.required],
        }),
      ),
    );
  }

  patchLogData(): void {
    if (this.log) {
      // Patch basic fields
      this.returnForm.patchValue({
        id: this.log.id,
        fuelIn: this.log.fuelIn,
        mileageIn: this.log.mileageIn,
        signatureIn: this.log.signatureIn,
        signatureInName: this.log.signatureInName,
        damageImageIn: this.log.damageImageIn,
        damageImageUploaded: this.log.damageImageUploaded,
      });

      // Patch damageInfoIn
      if (this.log.damageInfoIn) {
        this.damageInfoIn.clear();
        this.log.damageInfoIn.forEach((damage: any) => {
          this.damageInfoIn.push(
            this.fb.group({
              area: [damage.area, Validators.required],
              description: [damage.description, Validators.required],
              charge: [damage.charge, Validators.required],
            }),
          );
        });
      }

      // Patch items (check-out items)
      if (this.log.checkoutItems) {
        const checkoutItemsArray = this.log.checkoutItems
          .split(",")
          .map((item: string) => item.trim());
        this.items.clear();
        checkoutItemsArray.forEach((item: string) => {
          this.items.push(
            this.fb.group({
              name: [item], // Item name from backend
              systemChecked: [true], // System checked (pre-populated, disabled)
              userChecked: [false], // User confirmation checkbox
            }),
          );
        });
      }

      this.cdr.detectChanges(); // Ensure the view updates
    }
  }

  addDamage(): void {
    this.damageInfoIn.push(
      this.fb.group({
        area: ["", Validators.required],
        charge: ["", Validators.required],
        description: ["", Validators.required],
      }),
    );
  }

  removeDamage(index: number): void {
    this.damageInfoIn.removeAt(index); // Remove from FormArray
    this.comments.splice(index, 1); // Optionally remove from comments array
  }

  // Map comments to form data
  updateDamageInfoIn(): void {
    this.damageInfoIn.clear(); // Clear the existing form array

    this.comments.forEach((comment) => {
      this.damageInfoIn.push(
        this.fb.group({
          area: [comment.id, Validators.required], // Comment ID as area
          charge: [comment.charge || 0, [Validators.required, Validators.min(0)]], // Comment charge
          description: [comment.text, Validators.required], // Comment text as description
        }),
      );
    });
  }

  handleImageClick(event: MouseEvent) {
    if (event.target instanceof HTMLImageElement) {
      const rect = event.target.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      this.newCommentPosition = { x, y };
      console.log("New Comment Position:", this.newCommentPosition);
      event.stopPropagation();
    } else {
      console.error("Click event target is not an image.");
    }
  }

  addComment(): void {
    if (!this.newCommentPosition || !this.newCommentText.trim()) {
      this.toast.error("Please provide a valid comment and position.");
      return;
    }

    const newComment = {
      id: this.comments.length + 1,
      x: this.newCommentPosition.x,
      y: this.newCommentPosition.y,
      text: this.newCommentText.trim(),
      charge: 0,
      timestamp: new Date(),
    };

    this.comments.push(newComment);

    this.damageInfoIn.push(
      this.fb.group({
        area: [newComment.id, Validators.required],
        description: [newComment.text, Validators.required],
        charge: [0, [Validators.required, Validators.min(0)]],
      }),
    );

    console.log("Updated damageInfoIn:", this.damageInfoIn.value);

    this.newCommentText = "";
    this.newCommentPosition = null;

    this.toast.success("Comment added successfully.");
    console.log(this.damageInfoIn);
  }

  cancelNewComment() {
    this.newCommentPosition = null;
    this.newCommentText = "";
  }

  selectComment(comment: Comment, event: Event) {
    event.stopPropagation();
    this.selectedComment = comment;
  }

  deleteComment(index: number): void {
    this.comments.splice(index, 1);

    this.damageInfoIn.removeAt(index);
  }

  dataURLToBlob(dataURL: string): Promise<Blob> {
    return fetch(dataURL)
      .then((res) => res.blob())
      .catch((error) => {
        console.error("Error converting data URL to Blob:", error);
        throw error;
      });
  }

  async captureScreenshot(): Promise<string | null> {
    try {
      const canvas = await html2canvas(this.imageContainer.nativeElement, {
        useCORS: true,
        logging: true,
        scale: 2,
        scrollX: 0,
        scrollY: 0,
        windowWidth: document.documentElement.scrollWidth,
        windowHeight: document.documentElement.scrollHeight,
      });
      const base64Image = canvas.toDataURL("image/jpeg");
      const blob = await this.dataURLToBlob(base64Image);
      const file = new File([blob], "annotated-image.jpg", {
        type: "image/jpeg",
      });

      const formData = new FormData();
      formData.append("file", file);
      const response = await this.bookingService.uploadImage(formData).toPromise();

      if (response && response.fileUrl) {
        return response.fileUrl;
      } else {
        console.error("Image upload failed.");
        return null;
      }
    } catch (error) {
      console.error("Error capturing screenshot:", error);
      return null;
    }
  }

  openSignatureModal(modal: any): void {
    const modalRef = this.modalService.open(modal, {
      size: "lg",
      centered: true,
      windowClass: "return-signature-modal",
      backdropClass: "return-signature-modal-backdrop",
    });

    modalRef.result.then(
      (result) => {
        console.log(`Modal closed with: ${result}`);
      },
      (reason) => {
        console.log(`Modal dismissed with reason: ${reason}`);
      },
    );

    setTimeout(() => {
      if (this.signatureCanvasRef) {
        this.initializeCanvas();
      } else {
        console.error("Canvas element not found.");
      }

      const modalDialog = document.querySelector(".modal-dialog");
      if (modalDialog) {
        (modalDialog as HTMLElement).focus();
      }
    }, 100);
  }

  openCustomerSignatureModal(modal: any): void {
    const modalRef = this.modalService.open(modal, {
      size: "lg",
      centered: true,
      windowClass: "return-signature-modal",
      backdropClass: "return-signature-modal-backdrop",
    });

    modalRef.result.then(
      (result) => {
        console.log(`Customer signature modal closed with: ${result}`);
      },
      (reason) => {
        console.log(`Customer signature modal dismissed with reason: ${reason}`);
      },
    );

    setTimeout(() => {
      if (this.signatureCanvasRef) {
        this.initializeCanvas();
      } else {
        console.error("Canvas element not found.");
      }

      const modalDialog = document.querySelector(".modal-dialog");
      if (modalDialog) {
        (modalDialog as HTMLElement).focus();
      }
    }, 100);
  }

  closeModal(): void {
    this.modalService.dismissAll();
  }

  // ...existing code...
  initializeCanvas(): void {
    if (!this.signatureCanvasRef) {
      console.error("Canvas element is not defined.");
      return;
    }

    const canvas = this.signatureCanvasRef.nativeElement;
    this.canvasContext = canvas.getContext("2d");

    if (!this.canvasContext) {
      console.error("Failed to get canvas context.");
      return;
    }

    // Make canvas responsive by setting its dimensions to match its display size
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight || 200; // Fallback height

    // Update canvas dimensions
    canvas.width = displayWidth;
    canvas.height = displayHeight;

    this.canvasContext.fillStyle = "white";
    this.canvasContext.fillRect(0, 0, canvas.width, canvas.height);
    this.canvasContext.lineWidth = 2;
    this.canvasContext.lineCap = "round";
    this.canvasContext.strokeStyle = "black";

    // Mouse events
    canvas.addEventListener("mousedown", this.startDrawing.bind(this));
    canvas.addEventListener("mousemove", this.draw.bind(this));
    canvas.addEventListener("mouseup", this.stopDrawing.bind(this));
    canvas.addEventListener("mouseleave", this.stopDrawing.bind(this));

    // Touch events
    canvas.addEventListener("touchstart", this.startDrawingTouch.bind(this), {
      passive: false,
    });
    canvas.addEventListener("touchmove", this.drawTouch.bind(this), {
      passive: false,
    });
    canvas.addEventListener("touchend", this.stopDrawing.bind(this));
  }

  // Handle touch start event
  startDrawingTouch(event: TouchEvent): void {
    if (!this.canvasContext) return;
    event.preventDefault();
    this.drawing = true;
    const touch = event.touches[0];
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.beginPath();
    this.canvasContext.moveTo(touch.clientX - rect.left, touch.clientY - rect.top);
  }

  // Handle touch move event
  drawTouch(event: TouchEvent): void {
    if (!this.drawing || !this.canvasContext) return;
    event.preventDefault();
    const touch = event.touches[0];
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.lineTo(touch.clientX - rect.left, touch.clientY - rect.top);
    this.canvasContext.strokeStyle = "black";
    this.canvasContext.lineWidth = 2;
    this.canvasContext.stroke();
  }

  startDrawing(event: MouseEvent): void {
    if (!this.canvasContext) return;
    this.drawing = true;
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.beginPath();
    this.canvasContext.moveTo(event.clientX - rect.left, event.clientY - rect.top);
  }

  draw(event: MouseEvent): void {
    if (!this.drawing || !this.canvasContext) return;
    const rect = this.signatureCanvasRef.nativeElement.getBoundingClientRect();
    this.canvasContext.lineTo(event.clientX - rect.left, event.clientY - rect.top);
    this.canvasContext.strokeStyle = "black";
    this.canvasContext.lineWidth = 2;
    this.canvasContext.stroke();
  }

  stopDrawing(): void {
    this.drawing = false;
  }

  clearSignature(): void {
    if (!this.canvasContext) return;
    this.canvasContext.clearRect(
      0,
      0,
      this.signatureCanvasRef.nativeElement.width,
      this.signatureCanvasRef.nativeElement.height,
    );
  }

  saveSignature(modal: any): void {
    if (!this.signatureCanvasRef) {
      this.toast.error("Signature canvas is not initialized.");
      return;
    }

    const canvas = this.signatureCanvasRef.nativeElement;

    // Convert canvas to Blob as JPG
    canvas.toBlob((blob) => {
      if (!blob) {
        this.toast.error("Failed to generate signature image.");
        return;
      }

      const file = new File([blob], "signature.jpg", { type: "image/jpeg" });
      const formData = new FormData();
      formData.append("file", file);

      // Call API to upload the signature
      this.bookingService.uploadSignature(formData).subscribe({
        next: (response) => {
          if (response && response.fileUrl) {
            this.savedSignature = response.fileUrl; // Update savedSignature with the uploaded file URL
            this.returnForm.get("signatureInCarRental")?.setValue(response.fileUrl);
            console.log("Representative signature URL saved:", response.fileUrl);
          } else {
            console.error("API response does not contain fileUrl:", response);
            this.toast.error("Failed to retrieve the signature URL.");
          }
          modal.close();
        },
        error: (error) => {
          console.error("Error uploading signature:", error);
          this.toast.error("Failed to upload the signature.");
        },
      });
    }, "image/jpeg");
  }

  saveCustomerSignature(modal: any): void {
    if (!this.signatureCanvasRef) {
      this.toast.error("Signature canvas is not initialized.");
      return;
    }

    const canvas = this.signatureCanvasRef.nativeElement;

    // Convert canvas to Blob as JPG
    canvas.toBlob((blob) => {
      if (!blob) {
        this.toast.error("Failed to generate signature image.");
        return;
      }

      const file = new File([blob], "customer-signature.jpg", { type: "image/jpeg" });
      const formData = new FormData();
      formData.append("file", file);

      // Call API to upload the customer signature
      this.bookingService.uploadSignature(formData).subscribe({
        next: (response) => {
          if (response && response.fileUrl) {
            this.savedCustomerSignature = response.fileUrl; // Update savedCustomerSignature with the uploaded file URL
            this.returnForm.get("signatureInHirer")?.setValue(response.fileUrl);
            console.log("Customer signature URL saved:", response.fileUrl);
          } else {
            console.error("API response does not contain fileUrl:", response);
            this.toast.error("Failed to retrieve the customer signature URL.");
          }
          modal.close();
        },
        error: (error) => {
          console.error("Error uploading customer signature:", error);
          this.toast.error("Failed to upload the customer signature.");
        },
      });
    }, "image/jpeg");
  }

  // Add property to store the modal reference
  private ratingModalRef: NgbModalRef | null = null;

  async initiateVehicleCheck(): Promise<void> {
    this.hasSubmitted.set(true);
    if (this.returnForm.invalid || this.mileageError) {
      this.toast.error("Please fill out all required fields.");
      this.hasSubmitted.set(false);
      return;
    }

    // Store the modal reference
    this.ratingModalRef = this.modalService.open(this.hirerRatingModal, {
      centered: true,
      size: "lg",
      backdrop: "static", // Prevent closing by clicking outside
      keyboard: false, // Prevent closing with keyboard
    });

    try {
      await this.ratingModalRef.result;
      await this.handleSubmit();
    } catch (dismissReason) {
      await this.handleSubmit();
    }
  }

  submitRating(): void {
    if (!this.log.id) {
      this.toast.error("Booking ID not found");
      return;
    }

    const hasZeroRating = this.ratingItems.some((item) => item.rate === 0);
    const isCommentEmpty = !this.ratingComment || this.ratingComment.trim() === "";

    if (hasZeroRating || isCommentEmpty) {
      this.toast.error("Please provide all ratings and a comment");
      return;
    }

    this.isSubmittingRating.set(true);

    const payload = {
      comment: this.ratingComment,
      ratingItems: this.ratingItems,
    };

    this.bookingService.rateClientForBooking(this.log.id.toString(), payload).subscribe({
      next: () => {
        this.toast.success("Rating submitted successfully");
        this.isSubmittingRating.set(false);
        if (this.ratingModalRef) {
          this.ratingModalRef.close();
        }
      },
      error: (error) => {
        console.error("Rating submission error:", error);
        this.toast.error("Failed to submit rating");
        this.isSubmittingRating.set(false);
      },
      complete: () => {
        this.isSubmittingRating.set(false);
      },
    });
  }

  private async waitForUIUpdate(): Promise<void> {
    this.cdr.detectChanges();
    await new Promise((resolve) => setTimeout(resolve, 0));
  }

  async handleSubmit(): Promise<void> {
    if (this.isSubmitting()) return; // Prevent double submission

    // If there's an open comment box, close it
    if (this.newCommentPosition) {
      this.cancelNewComment();
      await this.waitForUIUpdate();
    }

    this.isSubmitting.set(true);

    try {
      const damagePhotos = this.uploadUrls.map((url) => ({ url }));
      const selectedItems = this.items.controls
        .filter((control) => control.get("userChecked")?.value)
        .map((control) => control.get("name")?.value)
        .join(", ");

      // Capture screenshot
      const uploadedImageUrl = await this.captureScreenshot();

      const extraData = this.extraCharges.value.map((item: any) => ({
        area: item.area,
        description: item.description,
        charge: item.charge || 0,
        type: item.type,
      }));

      const {
        fuelIn,
        id,
        mileageIn,
        signatureInCarRental,
        signatureInCarRentalName,
        signatureInHirer,
        signatureInHirerName,
      } = this.returnForm.value;

      // Debug log to check signature values
      console.log("Form values before submission:", {
        signatureInHirer,
        signatureInHirerName,
        signatureInCarRental,
        signatureInCarRentalName,
        savedCustomerSignature: this.savedCustomerSignature,
        savedSignature: this.savedSignature,
      });

      // Get damage info from the FormArray
      const damageInfoData = this.damageInfoIn.value.map((damage: any) => ({
        area: damage.area,
        description: damage.description,
        charge: damage.charge || 0,
      }));

      const payload = {
        fuelIn,
        id,
        damageImageIn: uploadedImageUrl,
        mileageIn,
        signatureInCarRental,
        signatureInCarRentalName,
        signatureInHirer,
        signatureInHirerName,
        checkoutItems: selectedItems,
        damageInfoIn: damageInfoData,
        extraCharges: extraData,
        damagePhotos,
      };

      this.bookingService.returnVehicle(payload).subscribe({
        next: (response) => {
          this.toast.success("Vehicle return details submitted successfully!");
          this.modalService.dismissAll();
          this.resetForm();
          this.updated.emit(true);
        },
        error: () => {
          this.toast.error("An error occurred while submitting the form.");
          this.isSubmitting.set(false);
        },
        complete: () => {
          this.isSubmitting.set(false);
        },
      });
    } catch (error) {
      this.isSubmitting.set(false);
      this.toast.error("An error occurred during the image upload or form submission.");
    }
  }

  resetForm(): void {
    this.returnForm.reset(); // Reset the main form
    this.damageInfoIn.clear(); // Clear the dynamic FormArray
    this.savedSignature = null; // Clear the saved representative signature
    this.savedCustomerSignature = null; // Clear the saved customer signature
    this.comments = []; // Clear the comments list
    this.newCommentText = "";
    this.newCommentPosition = null;
    this.mileageError = "";

    // ✅ Notify commentService to clear stored comments
    this.commentService.updateComments([]);

    // ✅ Force UI update
    this.cdr.detectChanges();
  }

  async downloadPDF() {
    this.isGeneratingPDF.set(true);
    try {
      await this.pdfService.generatePDF("check-in-form", "check-in-form");
      this.toast.success("PDF generated successfully. Check your downloads");
    } catch (error) {
      console.log("Error downloading the PDF");
      console.log(error);
      this.toast.error("Failed to generate PDF");
    } finally {
      this.isGeneratingPDF.set(false);
    }
  }

  setRating(item: RatingItem, rating: number): void {
    item.rate = rating;
  }

  get sortedDamageInfo(): any[] {
    if (!this.log?.damageInfoOut) return [];

    return [...this.log.damageInfoOut].sort((a, b) => {
      // Convert area strings to numbers for proper numerical sorting
      const areaA = parseInt(a.area, 10);
      const areaB = parseInt(b.area, 10);
      return areaA - areaB;
    });
  }

  getReturnMileageLimit() {
    const daysHired = this.calculateDaysHired();
    const totalMileageAllowance = this.getTotalMileageAllowance();

    // Determine starting mileage based on booking state
    let startingMileage: number;
    if (this.log.status === "BOOKED") {
      startingMileage = this.log.vehicle.mileage;
    } else {
      startingMileage = this.log.mileageOut ?? 0;
    }

    const totalAllowedMileage = daysHired * totalMileageAllowance;

    return totalAllowedMileage === 0 ? 0 : startingMileage + totalAllowedMileage;
  }
}
