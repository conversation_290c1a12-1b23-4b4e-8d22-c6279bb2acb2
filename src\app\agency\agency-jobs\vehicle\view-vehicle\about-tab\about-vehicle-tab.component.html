<form let [formGroup]="form" (ngSubmit)="updateVehicle()">
  <div class="d-flex">
    <h5>Vehicle Details</h5>
    <button
      *ngIf="(!form.enabled && userType == 'agency') || 'admin'"
      type="button"
      class="btn-primary btn ml-auto"
      (click)="enableEdit()"
    >
      Edit
    </button>
  </div>

  <div class="card p-2 mt-2 bg-grey">
    <div class="row">
      <div class="form-group col-6 col-sm-3 col-md-2">
        <label>Make</label>
        <app-vehicle-make-selector formControlName="name" />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-2">
        <label>Model</label>
        <input
          disabled
          type="text"
          formControlName="model"
          maxlength="255"
          placeholder="Vehicle Model"
          class="form-control"
        />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-2">
        <label>Colour</label>
        <input
          type="text"
          formControlName="color"
          maxlength="255"
          placeholder=""
          class="form-control"
        />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Reg Number</label>
        <input
          type="text"
          formControlName="regno"
          maxlength="255"
          placeholder=""
          class="form-control"
        />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Vehicle Type</label>
        <select class="form-control" formControlName="type" placeholder="">
          <option value="" disabled>Vehicle Type</option>
          <option value="SUV">SUV</option>
          <option value="PICKUP">PICK-UP/BAKKIE</option>
          <option value="HATCHBACK">HATCHBACK</option>
          <option value="MPV">MPV</option>
          <option value="SEDAN">SEDAN</option>
          <option value="SPECIAL">SPECIAL/LUXURY</option>
          <option value="BUS">BUS</option>
        </select>
      </div>
    </div>
  </div>

  <div class="card p-2 mt-2 bg-grey">
    <div class="row">
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Seats</label>
        <app-seats-selector formControlName="seats"></app-seats-selector>
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Doors</label>
        <app-doors-selector formControlName="doors"></app-doors-selector>
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Fuel Type </label>
        <select class="form-control" formControlName="fuelType" placeholder="">
          <option value="" disabled>Fuel Type</option>
          <option value="PETROL">Petrol</option>
          <option value="DIESEL">Diesel</option>
          <option value="PHYBRID">Petrol Hybrid</option>
          <option value="DHYBRID">Diesel Hybrid</option>
          <option value="ELECTRIC">Electric</option>
          <option value="HYDROGEN">Hydrogen</option>
        </select>
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Transmission</label>
        <select class="form-control" formControlName="transmissionType" placeholder="">
          <option value="" disabled>Transmission Type</option>
          <option value="AUTO">Auto</option>
          <option value="MANUAL">Manual</option>
        </select>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label for="description">Vehicle Description</label>
          <textarea
            id="description"
            formControlName="description"
            class="form-control"
            rows="4"
            placeholder="Enter description (max 160 characters)"
          ></textarea>
        </div>
      </div>
      <div class="d-flex flex-column justify-content-center col-6 col-sm-3 col-md-4">
        <div class="mt-3">
          <label>Air Conditioning</label>
          <input
            type="checkbox"
            maxlength="255"
            formControlName="airConditioning"
            placeholder="Capacity"
            [value]="true"
          />
        </div>
      </div>
    </div>
  </div>

  <h5>Other</h5>
  <div class="card p-2 bg-grey">
    <div class="row">
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Engine Number</label>
        <input
          type="text"
          maxlength="255"
          formControlName="engineNumber"
          placeholder=""
          class="form-control"
        />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Engine Capacity</label>
        <input
          type="text"
          maxlength="255"
          formControlName="engineSize"
          placeholder=""
          class="form-control"
        />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Tracker Id</label>
        <input
          type="text"
          maxlength="255"
          formControlName="trackerId"
          placeholder=""
          class="form-control"
        />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-3">
        <label>Mileage</label>
        <input
          type="text"
          maxlength="255"
          formControlName="mileage"
          placeholder=""
          class="form-control"
        />
      </div>
    </div>
  </div>

  <h5>Vehicle base details</h5>
  <div class="card p-2 bg-grey">
    <div class="row">
      <div class="form-group col-6 col-sm-3 col-md-4">
        <label>Contact person</label>
        <input
          type="text"
          formControlName="contactPerson"
          maxlength="255"
          placeholder=""
          class="form-control"
        />
      </div>
      <div class="col-6 col-sm-3 col-md-4">
        <label>Phone</label>
        <phone-input type="text" formControlName="contactPhone" maxlength="255" placeholder="" />
      </div>
      <div class="form-group col-6 col-sm-3 col-md-4">
        <label>Address</label>
        <input
          type="text"
          formControlName="contactAddress"
          maxlength="255"
          placeholder=""
          class="form-control"
        />
      </div>
      <div class="form-group col-lg-8 col-sm-3 col-md-4">
        <label for="locations">Locations</label>
        <app-multi-location-selector
          formControlName="locationIds"
          id="locations"
          [locations]="locations"
          placeholder="Select vehicle locations"
        />
      </div>

      <!-- <div class="col-md-4">
        <div class="form-group">
          <label for="description">Description</label>
          <textarea
            id="description"
            formControlName="description"
            class="form-control"
            rows="4"
            placeholder="Enter description (max 160 characters)"
          ></textarea>

        </div>
      </div> -->
    </div>
  </div>

  <div class="mt-1 btn-group">
    <button *ngIf="form.enabled" type="button" (click)="disableForm()" class="btn-danger mr-1 btn">
      Cancel
    </button>
    <button *ngIf="form.enabled" type="submit" class="btn-primary btn">Save Details</button>
  </div>

  <div *ngIf="!form.enabled" class="d-flex mt-2">
    <div class="col-12 mt-2 text-right">
      <button class="btn btn-danger" onclick="history.back()">Back</button>
      <button type="submit" class="btn btn-primary" (click)="goNext()">Next</button>
    </div>
  </div>
</form>
