.eventClass a {
  background-color: #dda919 !important;
  color: #fff !important;
}

button.example-custom-date-class {
  background: rgb(49, 120, 252);
  border-radius: 100%;
}

.cdk-overlay-container {
  z-index: 1070;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
}

.vehicle-addons-container {
  width: 100%;
}

.title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 0.9rem;
  color: gray;
}

.addon-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.add-on-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.input-group {
  display: flex;
  align-items: center;
  width: 100%;
}

.addon-input {
  flex: 1;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid #ccc;
  height: 40px;
}

/* .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
} */

.btn-remove {
  background: #ff9800;
  color: white;
  border: none;
}

.btn-add {
  background: #007bff;
  color: white;
  border: none;
  width: 100%;
  height: 40px;
}

.remove-icon,
.add-icon {
  font-size: 1.2rem;
  margin-right: 5px;
}

.sortable {
  cursor: pointer;
  user-select: none;
}

.sortable:hover {
  color: blue;
  text-decoration: underline;
}

.pointer {
  cursor: pointer;
}

.locations-column {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.locations-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 18px;
  font-size: 11px;
  line-height: 1;
  flex-shrink: 0;
}
